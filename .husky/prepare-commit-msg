#!/bin/sh

. "$(dirname "$0")/_/husky.sh"

commitMsgFilePath=$1
commitSource=$2
commitMsgContent=$(cat "$commitMsgFilePath")

# 格式: [JIRA-1234](scope): subject
# 示例: [PROJ-101](feat): add user login feature
regString='^\[[A-Z0-9_-]+\]\((build|ci|docs|feat|fix|perf|refactor|style|test)\): .+$'

# merge 不做此校验
if [ "${commitSource}" = merge ];then
    exit 0
fi

# 检验是否符合格式
if [[ $commitMsgContent =~ $regString ]]
then
    exit 0
fi

# 格式错误提示
echo "----------------------------------------------------------------"
echo "错误：提交信息不符合规范！"
echo "正确格式: [故事/任务编号](类型): 描述信息"
echo "例如: [PROJ-101](feat): 添加用户登录功能"
echo ""
echo "允许的 <类型> 包括:"
echo "  feat:     新功能"
echo "  fix:      修复 Bug"
echo "  docs:     仅文档变更"
echo "  style:    代码格式（不影响功能）"
echo "  refactor: 代码重构"
echo "  perf:     性能优化"
echo "  test:     增加或修改测试"
echo "  build:    影响构建系统或外部依赖的变更"
echo "  ci:       CI/CD 配置文件和脚本的变更"
echo "----------------------------------------------------------------"

exit 1
