## Galaxy Vue Demi 模板

本文档旨在为 Vue 项目提供一套统一的开发规范，涵盖项目结构、编码风格、组件设计、性能优化等方面，以提升代码质量、开发效率和项目可维护性。

## 1. 项目结构规范

推荐采用以下分层清晰、职责明确的目录结构：

```
vue-project/
├── public/                     # 静态资源根目录 (打包时直接复制)
│   ├── index.html             
│   └── favicon.ico
├── src/                        # 源代码目录
│   ├── api/                    # API 请求管理
│   │   ├── modules/            # 按业务模块划分的 API
│   │   │   ├── user.js
│   │   │   └── product.js
│   │   └── index.js            # Axios 实例、拦截器、统一配置等
│   ├── assets/                 # 本地静态资源 (会被 Webpack/Vite 处理)
│   │   ├── images/
│   │   ├── icons/
│   │   └── fonts/
│   ├── components/             # 全局通用组件
│   │   ├── base/               # 基础组件 (无业务逻辑，高度可复用)
│   │   │   ├── DataGrid.vue
│   │   │   └── BaseInput/
│   │   ├── business/           # 全局业务组件 (不推荐，见说明)
│   │   ├── icons/              # SVG 图标组件
│   │   └── layout/             # 布局相关组件
│   │       ├── AppFooter.vue
│   │       └── AppNavbar.vue
│   ├── composables/            # 全局组合式函数 (不推荐，应优先将组合式函数放在其关联视图的 composables 目录下)
│   ├── context/                # 上下文状态管理 (推荐)
│   │   ├── auth/               # 认证上下文 (如用户信息、权限)
│   │   │   ├── index.js        # 导出 useAuth
│   │   │   └── context.js      # 实现 provide/inject
│   │   └── business/           # 业务上下文 (如产品、钱包)
│   │       └── wallet/
│   ├── directives/             # 全局自定义指令 (不推荐，见说明)
│   ├── layouts/                # 页面布局
│   ├── views/                  # 视图 (页面) 目录
│   │   └── home/               # 单个视图，如“首页”
│   │       ├── components/     # 视图内部的子组件 (高内聚)
│   │       ├── composables/    # 视图内部的组合式函数 (高内聚)
│   │       └── index.vue       # 视图主组件
│   ├── router/                 # 路由配置
│   │   ├── modules/            # 按业务模块拆分路由 (可选)
│   │   └── index.js            # 主路由文件
│   ├── store/                  # 全局状态管理 (如 Pinia/Vuex) (不推荐，见说明)
│   │   ├── modules/
│   │   └── index.js
│   ├── styles/                 # 全局样式（可以按需添加）
│   │   ├── abstracts/          # SCSS 工具: 变量, 函数, 混合器
│   │   ├── base/               # 基础样式: 字体，Reset, 排版等
│   │   ├── utilities/          # 工具类
│   │   ├── components/         # 可复用组件样式
│   │   ├── vendors/            # 第三方库样式覆盖
│   │   └── main.scss           # 全局样式入口
│   ├── utils/                  # 全局工具函数 (不推荐，见说明)
│   ├── App.vue                 # 根组件
│   └── main.js                 # 应用入口文件
├── tests/                      # 测试文件
├── .env                        # 基础环境变量（按需添加环境）
├── .env.development            # 开发环境变量
├── .env.production             # 生产环境变量
├── .env.xxxx                   # 其它变量
├── .husky/                     # Husky Git Hooks 配置
├── .eslintrc.js                # ESLint 配置文件
├── .gitignore                  # Git 忽略文件配置
├── package.json                # 项目依赖与脚本配置
├── package-lock.json           # 锁定依赖版本
├── .npmrc                      # NPM 配置文件 (可选)
├── .nvmrc                      # Node.js 版本锁定文件
└── vite.config.js              # Vite / vue.config.js (Vue CLI) 配置文件
```

**目录使用说明与建议：**

* **`components/business`**: **不推荐使用**。业务组件应优先置于所属视图的 `views/.../components/`
  目录下，以增强模块内聚性，便于维护。此目录仅作为历史项目过渡或特殊情况下的备用。
* **`composables`**: **不推荐在根目录创建**。应优先将组合式函数放在其关联视图的 `views/.../composables/`
  目录下。仅当函数确实需要被多个无关视图复用时，再考虑提升至全局。
* **`context`**: **推荐使用**。用于替代传统的全局状态管理。通过 `provide/inject` 提供具有明确上下文的、响应式的状态与方法，比全局
  Store 更易于追踪和维护。
* **`directives`**: **不推荐自行封装**。应优先使用 Vue 和组件库自带的指令。自定义指令会增加项目复杂度，且在 Vue 3
  `<script setup>` 中使用不便。
* **`layouts`**: **不推荐创建此目录**。布局本身也是组件，建议统一归入 `components/layout/` 或作为特定视图的根组件，以简化项目结构。
* **`router/modules`**: 当路由配置非常庞大时（例如超过 50 条），可以按业务模块进行拆分。否则，建议在 `index.js`
  中集中管理，以降低查找和理解成本。
* **`store`**: **谨慎使用**。优先采用组件自身状态、`props/emits`、`composables` 或 `context`
  进行状态管理。避免无节制地使用全局状态，以防应用状态变得复杂且难以预测。
* **`utils`**: **谨慎使用**。为避免 `utils` 目录成为职责不清的“大杂烩”，建议遵循高内聚原则：
    * 请求相关的工具函数应放在 `api/` 目录下。
    * 特定领域的工具函数应封装成 `composables`，并与相关业务视图放在一起。
    * 表单校验逻辑建议直接在组件内部或对应的 `composables` 中实现。

## 2. CSS 规范

### 2.1. 技术栈

* **预处理器**: 推荐使用 SCSS，或者 LESS 作为轻量级的方案（H5）
    * SCSS 是 Element Plus 默认的预处理器 (由于 `node-sass` 不维护，推荐使用：`sass` / `sass-embedded`)
* **CSS 架构**: 采用 **ITCSS**（倒三角形 CSS）思想，结合 **7-1 模式**进行组织。
* **命名规范**: 大型前端项目建议遵循 **BEM** (Block\_\_Element--Modifier) 命名法。
    * **Block (块)**: 独立的功能组件，如 `.card`, `.nav`, `.search-form`。
    * **Element (元素)**: 块的组成部分，不能脱离块独立存在，使用 `__` 连接，如 `.card__header`, `.nav__item`。
    * **Modifier (修饰符)**: 描述块或元素的状态或变体，使用 `--` 连接，如 `.card--dark`, `.nav__item--active`。

### 2.2. 样式文件结构 (7-1 模式)

结合 Element Plus 提供的组件能力，`src/styles/` 目录结构遵循简化的 7-1 模式，确保样式的可预测性和可扩展性。

* **`abstracts/`**: Sass 工具集，不直接输出 CSS。
    * `_variables.scss`: 全局 CSS 变量（如间距、字号）。
    * `_colors.scss`: 颜色变量。
    * `_functions.scss`: Sass 函数。
    * `_mixins.scss`: Sass 混合器。
* **`base/`**: 项目基础样式，影响 HTML 元素的默认外观。
    * `_reset.scss`: 重置或标准化浏览器默认样式。
    * `_typography.scss`: 全局排版规则（如 `body`, `h1`, `p`）。
* **`components/`**: 用于存放复用组件的样式：
    * 如 `.dropdown`, `.search-form` 等
* layout/ (可选)：* 存放通用页面结构样式：如 `.grid`, `.container`
* **`vendors/`**: 第三方库的样式覆盖。
    * `_element-plus-theme.scss`: Element Plus 主题定制。
* **`utilities/`**: 高特异性的工具类（如 `.margin-top-10`），应谨慎使用。
* **`main.scss`**: 主样式入口文件，严格按照以下顺序导入，以符合 ITCSS 规范。

### 2.3 命名规范：BEM + 命名空间

推荐使用 **BEM** 命名规则，适用于大型项目的组件化场景：

* `Block`（块）：功能独立的 UI 单元，如 `.card`, `.nav`
* `Element`（元素）：组成 Block 的部分，使用 `__` 连接，如 `.card__header`
* `Modifier`（修饰符）：描述状态或外观变体，使用 `--` 连接，如 `.nav__item--active`

#### 命名空间前缀（可选）

为避免命名冲突、增强可读性，推荐为类名前添加上下文前缀：

| 前缀     | 含义           | 示例                        |
|--------|--------------|---------------------------|
| `c-`   | Component 组件 | `.c-card`, `.c-button`    |
| `l-`   | Layout 布局    | `.l-container`, `.l-grid` |
| `u-`   | Utility 工具类  | `.u-mt-20`, `.u-hidden`   |
| `is-`  | State 状态类    | `.is-active`, `.is-error` |
| `has-` | 状态（拥有）       | `.has-icon`, `.has-error` |

```scss
.c-card {
  border-radius: 4px;
}

.c-card--dark {
  background-color: #333;
}

.c-card__header {
  font-weight: bold;
}

.is-loading {
  opacity: 0.6;
}
```

## 3. 组件规范

### 3.1. 基础约定

* **命名**: 组件文件名和 `name` 属性均使用 **PascalCase**（大驼峰命名法），例如 `DataGrid.vue`。
* **单文件组件 (SFC) 结构顺序**:
    1. `<script setup>`: 组件的逻辑核心。
    2. `<template>`: 组件的 HTML 结构。
    3. `<style>`: 组件的样式，必须添加 `scoped` 或 `module` 属性以实现样式隔离。

### 3.2. 组件分类

* **`components/base/`**: 基础组件。不包含任何业务逻辑，可在任何场景下复用，如 `BaseButton`、`BaseInput`。
* **`components/layout/`**: 布局组件。用于构建页面的整体结构，如 `AppHeader`、`Sidebar`。
* **`views/.../components/`**: **业务组件**。与特定业务视图强相关，**应放置在对应视图的 `components` 目录中**。

### 3.3. 编码规范 (Vue 3)

* **Composition API**: 全面拥抱组合式 API，并优先使用 `<script setup>` 语法糖。
* **Props & Emits**: 使用 `defineProps` 和 `defineEmits` 宏定义清晰的组件接口。
* **逻辑复用**: 将可复用的、复杂的逻辑抽离到独立的组合式函数 (`composables`) 中。

### 3.4. JavaScript 与 TypeScript

* **JavaScript (JS)**：作为 Web 开发的基础语言，动态且灵活，入门门槛低，适合快速开发和小型项目。
* **TypeScript (TS)**：是 JS 的超集，增加了静态类型系统和更完善的类型检查，能够：
    * 提升代码的可维护性与可读性。
    * 提前发现潜在错误，减少运行时异常。
    * 支持现代 IDE 的智能提示和重构功能，提高开发效率。
    * 更适合大型项目和团队协作。

**性能与环境考量**：由于 TypeScript 需要编译为 JavaScript，编译过程和类型检查会带来一定的额外开销。在云主机或资源受限环境下，
构建性能可能受到影响。因此，在性能或资源有限的情况下，可以根据实际需求权衡是否采用 TypeScript，以兼顾开发效率与系统性能。


### 3.5. 原子设计思想（Atomic Design）

为了提升组件的复用性、可维护性和层次清晰度，建议在组件设计中引入 **原子设计（Atomic Design）** 思想。该思想将组件分为不同粒度的层级，
有助于结构化管理和规范组件开发。

组件层级定义与目录对应关系示例：

| 层级                 | 含义                            | 建议目录位置                        | 命名示例              | 说明                                |
|--------------------|-------------------------------|-------------------------------|-------------------|-----------------------------------|
| **原子（Atoms）**      | 最小的、不可再分的基础 UI 元素，如按钮、输入框、标签等 | `components/base/`            | `BaseButton.vue`  | 高度复用，无业务逻辑，命名带 `Base` 前缀          |
| **分子（Molecules）**  | 由多个原子组合形成的简单功能单元，如带图标的按钮组     | 视图目录下 `views/xxx/components/` | `UserAvatar.vue`  | 具有特定业务意义，紧耦合业务视图                  |
| **有机体（Organisms）** | 由分子和原子组合构成的复杂组件，如导航栏、表单等      | `components/layout/` 或视图目录    | `AppNavbar.vue`   | 较复杂且具通用性的布局或业务块                   |
| **模板（Templates）**  | 页面结构骨架，定义布局和内容位置              | `layouts/`                    | —                 | 建议布局归入 `components/layout/` 以简化结构 |
| **页面（Pages）**      | 具体视图页面，由模板填充实际数据              | `views/`                      | `UserProfile.vue` | 单个路由对应页面                          |

## 4. 路由规范

* **页面定义**。所有页面级组件必须统一存放在 `views/` 目录下。通常一个路由对应一个视图组件，保持结构清晰。
* **路由懒加载**。为所有视图组件启用懒加载，避免首屏加载过重，提高应用启动速度和用户体验。
* **命名规范**。路由的 `name` 属性统一采用 **kebab-case**（短横线命名法），保持命名风格一致，便于维护和查找。
* **动态路由**。动态路由参数使用冒号 `:` 标识，命名同样采用 kebab-case，参数应具备语义，方便理解。路由名称尽量体现动态参数，如 `user-profile` 对应用户列表，`user-profile-detail` 对应详情页。

示例：

```javascript
const routes = [
  {
    path: '/user-profile',
    name: 'user-profile',
    component: () => import('@/views/user/UserProfile.vue')
  },
  {
    path: '/user-profile/:user-id',
    name: 'user-profile-detail',
    component: () => import('@/views/user/UserProfileDetail.vue'),
    props: true // 建议开启 props 传参，方便组件接收参数
  }
];
```

## 5. 状态管理规范

### 库选择建议

* **新项目**：推荐使用 **Pinia**（Vue 3 官方推荐）。它更轻量、类型支持更好，且与 Vue 3 的组合式 API 协同良好。
* **迁移项目**：若现有项目使用 Vuex，且迁移工作量较大，可继续沿用 Vuex 并逐步向 Pinia 过渡。

推荐目录结构示例：

```bash
src/
├── context/                # 上下文状态（推荐优先使用）
│   ├── auth/
│   │   ├── context.ts      # provide/inject 实现
│   │   └── index.ts        # 暴露 useAuth 供外部使用
│   └── theme/
│       ├── context.ts
│       └── index.ts
├── store/                  # 全局状态（Pinia）
│   ├── user.ts             # 跨页面用户信息
│   └── settings.ts         # 系统设置、菜单状态等
```

### 状态管理优先级

在中小型 Vue 应用中，应优先采用轻量、可控的状态管理方式。推荐按照以下优先级选择：

1. **组件内部状态**：默认首选，适用于局部交互和界面状态。
2. **Props / Emits**：用于父子组件间的数据流动。
3. **Composables**：封装响应式逻辑，实现组件间共享状态。
4. **Context（Provide / Inject）**：用于多层级组件状态共享，推荐模块化管理。
5. **状态管理库（如 Pinia / Vuex）**：仅在状态需跨应用全局共享、逻辑复杂或存在高度解耦需求时使用。

> 不建议将全局状态管理作为默认选项。对于大部分业务需求，组合式 API 与模块化设计足以满足。

## 6. DevOps 与代码质量

### 6.1. ESLint

* **JavaScript**: 基础配置采用 `eslint:recommended` 和 `plugin:vue/vue3-essential`。
* **TypeScript**（如果采用的是 TypeScript）: 使用 `@vue/eslint-config-typescript` 进行集成。

### 6.2. Husky

通过 Husky 强制执行标准的提交信息格式。

* **配置**: 在 `package.json` 中配置 `husky` 相关脚本。
* **`pre-commit` 钩子**: 用于在提交前执行代码格式化和 Lint 检查。
* **`commit-msg` 钩子**: 用于校验提交信息是否符合约定格式。

**提交信息规范脚本示例 (`.husky/pre-commit-msg`)**

```sh
#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

commitMsgFilePath=$1
commitSource=$2
commitMsgContent=$(cat "$commitMsgFilePath")

# 格式: [JIRA-1234](scope): subject
# 示例: [PROJ-101](feat): add user login feature
regString='^\[[A-Z0-9_-]+\]\((build|ci|docs|feat|fix|perf|refactor|style|test)\): .+$'

# 合并操作不进行校验
if [ "${commitSource}" = "merge" ]; then
    exit 0
fi

if [[ $commitMsgContent =~ $regString ]]; then
    exit 0
fi

# 格式错误提示
echo "----------------------------------------------------------------"
echo "错误：提交信息不符合规范！"
echo "正确格式: [故事/任务编号](类型): 描述信息"
echo "例如: [PROJ-101](feat): 添加用户登录功能"
echo ""
echo "允许的 <类型> 包括:"
echo "  feat:     新功能"
echo "  fix:      修复 Bug"
echo "  docs:     仅文档变更"
echo "  style:    代码格式（不影响功能）"
echo "  refactor: 代码重构"
echo "  perf:     性能优化"
echo "  test:     增加或修改测试"
echo "  build:    影响构建系统或外部依赖的变更"
echo "  ci:       CI/CD 配置文件和脚本的变更"
echo "----------------------------------------------------------------"

exit 1
```

## 7. 性能优化指南

### 7.1. 包体积优化 (Bundle Size)

* **构建分析**: 使用 `webpack-bundle-analyzer` (Vue CLI) 或 `rollup-plugin-visualizer` (Vite) 定期分析包体构成。
* **路由懒加载**: 这是减小初始加载体积最有效的方式。
* **按需引入**: 确保对第三方库（如组件库、图表库）进行按需导入。
* **CDN**: 对于体积较大的稳定库（如 Vue, Vue Router, Axios），可以考虑使用 CDN 引入，并通过 `externals` 配置告知构建工具。
* **Tree-shaking**: 确保代码是 ES Module 格式，以便构建工具可以移除未使用的代码。

### 7.2. 运行时性能优化 (Runtime)

* **`v-memo`**: 对不常变化的模板片段进行缓存。
* **`v-once`**: 用于仅需渲染一次的静态内容。
* **`v-for` Key**: 必须为 `v-for` 循环中的每一项提供唯一的、稳定的 `key`。
* **`keep-alive`**: 合理使用 `keep-alive` 缓存非活动组件实例，避免重复创建和销毁。
* **计算属性**: 使用 `computed` 缓存复杂表达式的计算结果，避免在模板中进行重复计算。
* **虚拟列表**: 当渲染长列表（上千项）时，使用虚拟滚动技术（如 `vue-virtual-scroller`）来提高性能。
