<script setup lang="js">
import { useHead } from '@unhead/vue'
import settings from '@/settings'
import { computed, ref } from 'vue'

const defaultMeta = ref({
  title: settings.SEO.title,
  description: settings.SEO.description,
  keywords: settings.SEO.keywords,
})

// 当前页面的 SEO 配置
const pageMeta = ref({
  title: '',
  description: '',
  keywords: '',
})
const meta = computed(() => {
  return {
    title: pageMeta.value.title || defaultMeta.value.title,
    description: pageMeta.value.description || defaultMeta.value.description,
    keywords: pageMeta.value.keywords || defaultMeta.value.keywords,
  }
})
const updatePageMeta = (newMeta) => {
  pageMeta.value = { ...pageMeta.value, ...newMeta }
}
defineExpose({
  updatePageMeta,
})
useHead({
  title: computed(() => meta.value.title),
  meta: [
    {
      name: 'description',
      content: computed(() => meta.value.description),
    },
    {
      name: 'keywords',
      content: computed(() => meta.value.keywords),
    },
    {
      property: 'og:title',
      content: computed(() => meta.value.title),
    },
    {
      property: 'og:description',
      content: computed(() => meta.value.description),
    },
  ],
})
</script>

<template>
  <router-view />
</template>

<style scoped lang="scss">
@use '@/styles/abstracts/_colors.scss' as *;

header {
  line-height: 1.5;
  max-height: 100vh;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

nav {
  width: 100%;
  font-size: 12px;
  text-align: center;
  margin-top: 2rem;
}

nav a.router-link-exact-active {
  color: var(--color-text);
}

nav a.router-link-exact-active:hover {
  background-color: transparent;
}

nav a {
  display: inline-block;
  padding: 0 1rem;
  border-left: 1px solid var(--color-border);
}

nav a:first-of-type {
  border: 0;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }

  nav {
    text-align: left;
    margin-left: -1rem;
    font-size: 1rem;

    padding: 1rem 0;
    margin-top: 1rem;
  }
}
</style>
