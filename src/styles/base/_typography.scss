@use 'sass:map';

:root {
  // ===== 字号/行高 =====
  --font-size-page-title: 24px; // 页面标题
  --line-height-page-title: 33px;

  --font-size-h1: 20px; // 一级标题
  --line-height-h1: 28px;

  --font-size-h2: 18px; // 二级标题
  --line-height-h2: 25px;

  --font-size-body: 16px; // 正文文字
  --line-height-body: 22px;

  --font-size-content: 14px; // 内容文字
  --line-height-content: 20px;

  --font-size-small: 12px; // 内容文字
  --line-height-small: 17px;
}

@mixin page-title {
  font-size: var(--font-size-page-title);
  line-height: var(--line-height-page-title);
}

/*
* @mixin heading-1
* @description 一级标题样式，用于模块标题，导航文字
* @usage:
*   .module-title {
*     @include heading-1;
*   }
*
* @result:
*   .module-title {
*     font-size: 20px;
*     line-height: 28px;
*   }
*/
@mixin heading-1 {
  font-size: var(--font-size-h1);
  line-height: var(--line-height-h1);
}

/*
* @mixin heading-2
* @description 二级标题样式，用于模块内小标题
* @usage:
*   .sub-title {
*     @include heading-2;
*   }
*/
@mixin heading-2 {
  font-size: var(--font-size-h2);
  line-height: var(--line-height-h2);
}

/*
* @mixin body-text
* @description 正文文字样式，用于常规正文内容
* @usage:
*   .content-body {
*     @include body-text;
*   }
*/
@mixin body-text {
  font-size: var(--font-size-body);
  line-height: var(--line-height-body);
}

/*
* @mixin content-text
* @description 内容文字样式，用于日期，解释说明，底栏内容文字，次要文字，分页数字
* @usage:
*   .footer-text, .pagination {
*     @include content-text;
*   }
*/
@mixin content-text {
  font-size: var(--font-size-content);
  line-height: var(--line-height-content);
}

/*
* @mixin small-text
* @description 小文字样式，用于版权文字等次要信息
* @usage:
*   .copyright {
*     @include small-text;
*   }
*/
@mixin small-text {
  font-size: var(--font-size-small);
  line-height: var(--line-height-small);
}

/*
* @mixin text-style
* @description 通用文字样式，支持不同字号、字重和字体
* @params $type - 文字类型: page-title, h1, h2, body, content, small
* @params $weight - 字重: normal, bold
* @params $font-family - 字体: default, mono
* @usage:
*   .element {
*     @include text-style('page-title', 'bold');
*   }
*/
@mixin text-style($type, $weight: 'normal', $font-family: 'default') {
  @if $type == 'page-title' {
    font-size: var(--font-size-page-title);
    line-height: var(--line-height-page-title);
  } @else if $type == 'h1' {
    font-size: var(--font-size-h1);
    line-height: var(--line-height-h1);
  } @else if $type == 'h2' {
    font-size: var(--font-size-h2);
    line-height: var(--line-height-h2);
  } @else if $type == 'body' {
    font-size: var(--font-size-body);
    line-height: var(--line-height-body);
  } @else if $type == 'content' {
    font-size: var(--font-size-content);
    line-height: var(--line-height-content);
  } @else if $type == 'small' {
    font-size: var(--font-size-small);
    line-height: var(--line-height-small);
  }

  @if $weight == 'bold' {
    font-weight: bold;
  } @else if $weight == 'normal' {
    font-weight: normal;
  }

  @if $font-family == 'mono' {
    font-family: monospace;
  }
}

$typography-scale: (
  'page-title': (
    font-size: var(--font-size-page-title),
    line-height: var(--line-height-page-title)
  ),
  'h1': (
    font-size: var(--font-size-h1),
    line-height: var(--line-height-h1)
  ),
  'h2': (
    font-size: var(--font-size-h2),
    line-height: var(--line-height-h2)
  ),
  'body': (
    font-size: var(--font-size-body),
    line-height: var(--line-height-body)
  ),
  'content': (
    font-size: var(--font-size-content),
    line-height: var(--line-height-content)
  ),
  'small': (
    font-size: var(--font-size-small),
    line-height: var(--line-height-small)
  )
);
