// https://github.com/element-plus/element-plus/blob/dev/packages/theme-chalk/src/common/var.scss
@use '@/styles/abstracts/colors';
@use '@/styles/base/typography';
@use '@/styles/base/spacing';

:root {

  // ===== Primary Colors =====
  --el-color-primary: var(--color-brand-primary);

  --el-color-primary-light-3: var(--color-brand-hover);
  --el-color-primary-light-5: var(--color-brand-hover);
  --el-color-primary-light-7: var(--color-brand-hover);
  --el-color-primary-light-8: var(--color-brand-tag-bg);
  --el-color-primary-light-9: var(--color-brand-tag-bg);
  --el-color-primary-dark-2: var(--color-brand-active);

  --el-color-success: var(--color-functional-success);
  --el-color-success-light-3: color-mix(in srgb, var(--color-functional-success) 85%, white);
  --el-color-success-light-5: color-mix(in srgb, var(--color-functional-success) 75%, white);
  --el-color-success-light-7: color-mix(in srgb, var(--color-functional-success) 65%, white);
  --el-color-success-light-8: var(--color-functional-success-bg);
  --el-color-success-light-9: var(--color-functional-success-bg);
  --el-color-success-dark-2: var(--color-functional-success);

  --el-color-warning: var(--color-functional-warning);
  --el-color-warning-light-3: color-mix(in srgb, var(--color-functional-warning) 85%, white);
  --el-color-warning-light-5: color-mix(in srgb, var(--color-functional-warning) 75%, white);
  --el-color-warning-light-7: color-mix(in srgb, var(--color-functional-warning) 65%, white);
  --el-color-warning-light-8: var(--color-functional-warning-bg);
  --el-color-warning-light-9: var(--color-functional-warning-bg);
  --el-color-warning-dark-2: var(--color-functional-warning);

  --el-color-danger: var(--color-functional-danger);
  --el-color-danger-light-3: color-mix(in srgb, var(--color-functional-danger) 85%, white);
  --el-color-danger-light-5: color-mix(in srgb, var(--color-functional-danger) 75%, white);
  --el-color-danger-light-7: color-mix(in srgb, var(--color-functional-danger) 65%, white);
  --el-color-danger-light-8: var(--color-functional-danger-bg);
  --el-color-danger-light-9: var(--color-functional-danger-bg);
  --el-color-danger-dark-2: var(--color-functional-danger);

  --el-color-error: var(--color-functional-danger);
  --el-color-error-light-3: var(--color-functional-danger);
  --el-color-error-light-5: var(--color-functional-danger);
  --el-color-error-light-7: var(--color-functional-danger);
  --el-color-error-light-8: var(--color-functional-danger-bg);
  --el-color-error-light-9: var(--color-functional-danger-bg);
  --el-color-error-dark-2: var(--color-functional-danger);

  --el-color-info: var(--color-text-tertiary);
  --el-color-info-light-3: color-mix(in srgb, var(--color-text-tertiary) 85%, white);
  --el-color-info-light-5: color-mix(in srgb, var(--color-text-tertiary) 75%, white);
  --el-color-info-light-7: color-mix(in srgb, var(--color-text-tertiary) 65%, white);
  --el-color-info-light-8: var(--color-fill-tag);
  --el-color-info-light-9: var(--color-fill-tag);
  --el-color-info-dark-2: var(--color-text-tertiary);

  // ===== Text Colors =====
  --el-text-color-primary: var(--color-text-primary);
  --el-text-color-regular: var(--color-text-secondary);
  --el-text-color-secondary: var(--color-text-tertiary);
  --el-text-color-placeholder: var(--color-text-placeholder);
  --el-text-color-disabled: var(--color-text-primary-disabled);

  // ===== Border Colors =====
  --el-border-color: var(--color-border-base);
  --el-border-color-light: var(--color-border-base);
  --el-border-color-lighter: var(--color-border-base);
  --el-border-color-extra-light: var(--color-border-base);
  --el-border-color-dark: var(--color-border-base);
  --el-border-color-darker: var(--color-border-base);

  // ===== Fill Colors =====
  --el-fill-color: var(--color-fill-tag);
  --el-fill-color-light: var(--color-fill-disabled);
  --el-fill-color-lighter: var(--color-fill-disabled);
  --el-fill-color-extra-light: var(--color-fill-disabled);
  --el-fill-color-dark: var(--color-fill-emphasis);
  --el-fill-color-darker: var(--color-fill-emphasis);
  --el-fill-color-blank: #ffffff;

  // ===== Background Colors =====
  --el-bg-color: #ffffff;
  --el-bg-color-page: var(--color-fill-page-bg);
  --el-bg-color-overlay: #ffffff;

  // ===== Typography =====
  --el-font-size-extra-large: var(--font-size-page-title);
  --el-font-size-large: var(--font-size-h2);
  --el-font-size-medium: var(--font-size-body);
  --el-font-size-base: var(--font-size-content);
  --el-font-size-small: var(--font-size-small);
  --el-font-size-extra-small: var(--font-size-small);

  // ===== Disabled States =====
  --el-disabled-bg-color: var(--color-fill-disabled);
  --el-disabled-text-color: var(--color-text-primary-disabled);
  --el-disabled-border-color: var(--color-border-base);

  // ===== Component Specific Variables =====

  // Button
  --el-button-font-weight: 400;
  --el-button-border-color: var(--color-border-base);
  --el-button-bg-color: #ffffff;
  --el-button-text-color: var(--color-text-secondary);
  --el-button-disabled-text-color: var(--color-text-primary-disabled);
  --el-button-disabled-bg-color: var(--color-fill-disabled);
  --el-button-disabled-border-color: var(--color-border-base);
  --el-button-hover-text-color: var(--color-brand-primary);
  --el-button-hover-bg-color: var(--color-brand-tag-bg);
  --el-button-hover-border-color: var(--color-brand-hover);
  --el-button-active-text-color: var(--color-brand-primary);
  --el-button-active-border-color: var(--color-brand-primary);
  --el-button-active-bg-color: var(--color-brand-tag-bg);

  // Table
  --el-table-border-color: var(--color-border-base);
  --el-table-text-color: var(--color-text-secondary);
  --el-table-header-text-color: var(--color-text-primary);
  --el-table-header-bg-color: var(--color-brand-table-header);
  --el-table-header-border-color: var(--color-border-base);
  --el-table-bg-color: var(--color-fill-table-bg);
  --el-table-tr-bg-color: var(--color-fill-table-bg);
  --el-table-row-hover-bg-color: var(--color-fill-table-bg);
  --el-table-expanded-cell-bg-color: var(--color-fill-table-bg);
  --el-table-fixed-box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);

  // Input
  --el-input-text-color: var(--color-text-primary);
  --el-input-border: 1px solid var(--color-border-base);
  --el-input-hover-border: 1px solid var(--color-brand-hover);
  --el-input-focus-border: 1px solid var(--color-brand-primary);
  --el-input-transparent-border: 0 0 0 1px transparent inset;
  --el-input-border-color: var(--color-border-base);
  --el-input-border-radius: 4px;
  --el-input-bg-color: #ffffff;
  --el-input-icon-color: var(--color-text-placeholder);
  --el-input-placeholder-color: var(--color-text-placeholder);
  --el-input-hover-border-color: var(--color-brand-hover);
  --el-input-clear-hover-color: var(--color-text-secondary);
  --el-input-focus-border-color: var(--color-brand-primary);

  // Select
  --el-select-border-color-hover: var(--color-brand-hover);
  --el-select-disabled-color: var(--color-text-primary-disabled);
  --el-select-disabled-border: var(--color-border-base);
  --el-select-font-size: var(--font-size-content);
  --el-select-close-hover-color: var(--color-text-secondary);
  --el-select-input-color: var(--color-text-placeholder);
  --el-select-multiple-input-color: var(--color-text-secondary);
  --el-select-input-focus-border-color: var(--color-brand-primary);
  --el-select-input-font-size: var(--font-size-content);

  // Dropdown
  --el-dropdown-menuItem-hover-fill: var(--color-brand-tag-bg);
  --el-dropdown-menuItem-hover-color: var(--color-brand-primary);

  // Menu
  --el-menu-active-color: var(--color-brand-primary);
  --el-menu-text-color: var(--color-text-primary);
  --el-menu-hover-text-color: var(--color-brand-primary);
  --el-menu-bg-color: #ffffff;
  --el-menu-hover-bg-color: var(--color-brand-tag-bg);
  --el-menu-item-height: 56px;
  --el-menu-sub-item-height: 50px;
  --el-menu-horizontal-height: 60px;
  --el-menu-horizontal-sub-item-height: 36px;
  --el-menu-item-font-size: var(--font-size-body);
  --el-menu-item-hover-fill: var(--color-brand-tag-bg);
  --el-menu-border-color: var(--color-border-base);
  --el-menu-base-level-padding: 20px;
  --el-menu-level-padding: 20px;
  --el-menu-icon-width: 24px;

  // Dialog
  --el-dialog-width: 50%;
  --el-dialog-margin-top: 15vh;
  --el-dialog-bg-color: #ffffff;
  --el-dialog-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
  --el-dialog-title-font-size: var(--font-size-h2);
  --el-dialog-content-font-size: var(--font-size-content);
  --el-dialog-font-line-height: var(--line-height-content);
  --el-dialog-padding-primary: 16px;
  --el-dialog-border-radius: 4px;

  // Message
  --el-message-bg-color: var(--color-fill-tag);
  --el-message-border-color: var(--color-border-base);
  --el-message-padding: 11px 15px;
  --el-message-close-size: 16px;
  --el-message-close-icon-color: var(--color-text-placeholder);
  --el-message-close-hover-color: var(--color-text-secondary);

  // Notification
  --el-notification-width: 330px;
  --el-notification-padding: 14px 26px 14px 13px;
  --el-notification-radius: 8px;
  --el-notification-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  --el-notification-border-color: var(--color-border-base);
  --el-notification-icon-size: 24px;
  --el-notification-close-font-size: 16px;
  --el-notification-group-margin-left: 13px;
  --el-notification-group-margin-right: 8px;
  --el-notification-content-font-size: var(--font-size-content);
  --el-notification-content-color: var(--color-text-secondary);
  --el-notification-title-font-size: var(--font-size-body);
  --el-notification-title-color: var(--color-text-primary);
  --el-notification-close-color: var(--color-text-secondary);
  --el-notification-close-hover-color: var(--color-text-secondary);

  // Card
  --el-card-border-color: var(--color-border-base);
  --el-card-border-radius: 4px;
  --el-card-padding: 20px;
  --el-card-bg-color: #ffffff;

  // Tag
  --el-tag-font-size: var(--font-size-small);
  --el-tag-border-radius: 4px;
  --el-tag-border-radius-rounded: 20px;

  // Checkbox
  --el-checkbox-font-size: var(--font-size-content);
  --el-checkbox-font-weight: 400;
  --el-checkbox-text-color: var(--color-text-secondary);
  --el-checkbox-input-height: 14px;
  --el-checkbox-input-width: 14px;
  --el-checkbox-border-radius: 2px;
  --el-checkbox-bg-color: #ffffff;
  --el-checkbox-input-border: 1px solid var(--color-border-base);
  --el-checkbox-disabled-border-color: var(--color-border-base);
  --el-checkbox-disabled-input-fill: var(--color-fill-disabled);
  --el-checkbox-disabled-icon-color: var(--color-text-placeholder);
  --el-checkbox-disabled-checked-input-fill: var(--color-border-base);
  --el-checkbox-disabled-checked-input-border-color: var(--color-border-base);
  --el-checkbox-disabled-checked-icon-color: var(--color-text-placeholder);
  --el-checkbox-checked-text-color: var(--color-brand-primary);
  --el-checkbox-checked-input-border-color: var(--color-brand-primary);
  --el-checkbox-checked-bg-color: var(--color-brand-primary);
  --el-checkbox-checked-icon-color: #ffffff;
  --el-checkbox-input-border-color-hover: var(--color-brand-primary);

  // Radio
  --el-radio-font-size: var(--font-size-content);
  --el-radio-text-color: var(--color-text-secondary);
  --el-radio-font-weight: 400;
  --el-radio-input-height: 14px;
  --el-radio-input-width: 14px;
  --el-radio-input-border-radius: 100%;
  --el-radio-input-bg-color: #ffffff;
  --el-radio-input-border: 1px solid var(--color-border-base);
  --el-radio-input-border-color: var(--color-border-base);
  --el-radio-input-border-color-hover: var(--color-brand-primary);

  // Pagination
  --el-pagination-font-size: var(--font-size-content);
  --el-pagination-bg-color: #ffffff;
  --el-pagination-text-color: var(--color-text-secondary);
  --el-pagination-border-radius: 4px;
  --el-pagination-button-color: var(--color-text-secondary);
  --el-pagination-button-bg-color: #ffffff;
  --el-pagination-button-disabled-color: var(--color-text-placeholder);
  --el-pagination-button-disabled-bg-color: var(--color-fill-disabled);
  --el-pagination-hover-color: var(--color-brand-primary);

  // Loading
  --el-loading-spinner-size: 42px;
  --el-loading-fullscreen-spinner-size: 50px;

  // Scrollbar
  --el-scrollbar-opacity: 0.3;
  --el-scrollbar-bg-color: var(--color-text-secondary);
  --el-scrollbar-hover-opacity: 0.5;
  --el-scrollbar-hover-bg-color: var(--color-text-secondary);

  // Switch
  --el-switch-on-color: var(--color-brand-primary);
  --el-switch-off-color: var(--color-border-base);

  // Slider
  --el-slider-main-bg-color: var(--color-brand-primary);
  --el-slider-runway-bg-color: var(--color-border-base);
  --el-slider-stop-bg-color: #ffffff;
  --el-slider-disabled-color: var(--color-text-placeholder);
  --el-slider-border-radius: 3px;
  --el-slider-height: 6px;
  --el-slider-button-size: 20px;
  --el-slider-button-wrapper-size: 36px;
  --el-slider-button-wrapper-offset: -15px;

  // DatePicker
  --el-datepicker-text-color: var(--color-text-secondary);
  --el-datepicker-off-text-color: var(--color-text-placeholder);
  --el-datepicker-header-text-color: var(--color-text-secondary);
  --el-datepicker-icon-color: var(--color-text-primary);
  --el-datepicker-border-color: var(--color-border-base);
  --el-datepicker-inner-border-color: var(--color-border-base);
  --el-datepicker-inrange-bg-color: var(--color-brand-tag-bg);
  --el-datepicker-inrange-hover-bg-color: var(--color-brand-tag-bg);
  --el-datepicker-active-color: var(--color-brand-primary);
  --el-datepicker-hover-text-color: var(--color-brand-primary);

  // Upload
  --el-upload-dragger-padding-horizontal: 40px;
  --el-upload-dragger-padding-vertical: 10px;

  // Progress
  --el-progress-text-color: var(--color-text-primary);
  --el-progress-text-size: var(--font-size-content);

  // Alert
  --el-alert-padding: 8px 16px;
  --el-alert-border-radius-base: 4px;
  --el-alert-title-font-size: var(--font-size-content);
  --el-alert-title-with-description-font-size: var(--font-size-body);
  --el-alert-description-font-size: var(--font-size-content);
  --el-alert-close-font-size: var(--font-size-body);
  --el-alert-close-customed-font-size: var(--font-size-content);
  --el-alert-icon-size: 16px;
  --el-alert-icon-large-size: 28px;

  // Breadcrumb
  --el-breadcrumb-font-size: var(--font-size-content);
  --el-breadcrumb-icon-font-size: var(--font-size-content);
  --el-breadcrumb-item-color: var(--color-text-secondary);
  --el-breadcrumb-separator-color: var(--color-text-placeholder);

  // Tabs
  --el-tabs-header-height: 40px;

  // Form
  --el-form-label-font-size: var(--font-size-content);

  // Divider
  --el-divider-text-color: var(--color-text-primary);
  --el-divider-border-color: var(--color-border-base);
}

.el-table {
  // 基础样式优化
  --el-table-border-color: var(--color-border-base);
  --el-table-header-bg-color: var(--color-brand-table-header);
  --el-table-header-text-color: var(--color-text-primary);
  --el-table-text-color: var(--color-text-secondary);

  // 表格单元格内边距优化
  --el-table-cell-padding: 12px 16px;

  // 行样式
  tr.el-table__row {
    background-color: #ffffff; /* 第一行为白色 */
    transition: background-color 0.2s ease;

    &:nth-child(even) {
      background-color: var(--color-fill-table-bg); /* 第二行使用 --color-fill-table-bg 颜色 */
    }

    // 鼠标悬停效果
    &:hover > td.el-table__cell {
      background-color: var(--color-brand-tag-bg) !important;
    }

    // 选中行样式
    &.current-row > td.el-table__cell {
      background-color: var(--color-brand-tag-bg) !important;
      color: var(--color-brand-primary);
      font-weight: 500;
    }
  }

  // 表头样式优化
  .el-table__header tr th.el-table__cell {
    background-color: var(--color-brand-table-header);
    font-weight: 600;
    color: var(--color-text-primary);
    border-bottom: 1px solid var(--color-border-base);
  }

  // 表格边框样式
  &.el-table--border {
    .el-table__cell {
      border-right: 1px solid var(--color-border-base);
    }
  }

  // 固定列样式
  .el-table__fixed-right, .el-table__fixed {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  }

  // 空数据样式
  .el-table__empty-block {
    .el-table__empty-text {
      color: var(--color-text-tertiary);
    }
  }
}
