@use '../abstracts/colors';
@use '../base/typography';
@use 'sass:map';

@each $name, $color in colors.$colors {
  .text-#{$name} {
    color: #{$color} !important;
  }
}

.text-success {
  color: var(--color-semantic-success) !important;
}
.text-warning {
  color: var(--color-semantic-warning) !important;
}
.text-danger {
  color: var(--color-semantic-danger) !important;
}
.text-info {
  color: var(--color-semantic-info) !important;
}
.text-primary {
  color: var(--color-semantic-primary) !important;
}
