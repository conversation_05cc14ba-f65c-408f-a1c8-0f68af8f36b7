<template>
  <div class="user-page">
    <PageHeader
      title="用户管理"
      description="管理系统用户，包括用户信息、权限设置等">
      <template #actions>
        <el-button type="primary" @click="handleCreateUser">
          <el-icon><Plus /></el-icon>
          新增用户
        </el-button>
      </template>
    </PageHeader>

    <!-- 搜索和筛选 -->
    <UserSearchForm
      v-model:filters="searchFilters"
      @search="handleSearch"
      @reset="handleReset" />

    <!-- 用户列表 -->
    <UserTable
      :data="users"
      :loading="loading"
      :pagination="pagination"
      @edit="handleEditUser"
      @delete="handleDeleteUser"
      @page-change="handlePageChange" />

    <!-- 用户表单对话框 -->
    <UserFormDialog
      v-model="dialogVisible"
      :user="currentUser"
      :mode="dialogMode"
      @submit="handleSubmitUser" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useStore } from 'vuex'
import PageHeader from '@/components/base/PageHeader/index.vue'
import UserSearchForm from './components/UserSearchForm.vue'
import UserTable from './components/UserTable.vue'
import UserFormDialog from './components/UserFormDialog.vue'

const store = useStore()

// 响应式数据
const users = ref([])
const loading = ref(false)
const dialogVisible = ref(false)
const currentUser = ref(null)
const dialogMode = ref('create') // create | edit

const searchFilters = reactive({
  keyword: '',
  status: '',
  role: '',
  dateRange: null
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 方法
const fetchUsers = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchFilters
    }

    const response = await store.dispatch('user/fetchUsers', params)
    users.value = response.data
    pagination.total = response.total

  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  fetchUsers()
}

const handleReset = () => {
  Object.assign(searchFilters, {
    keyword: '',
    status: '',
    role: '',
    dateRange: null
  })
  handleSearch()
}

const handleCreateUser = () => {
  currentUser.value = null
  dialogMode.value = 'create'
  dialogVisible.value = true
}

const handleEditUser = (user) => {
  currentUser.value = { ...user }
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

const handleDeleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await store.dispatch('user/deleteUser', user.id)
    ElMessage.success('删除成功')
    fetchUsers()

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmitUser = async (userData) => {
  try {
    if (dialogMode.value === 'create') {
      await store.dispatch('user/createUser', userData)
      ElMessage.success('创建成功')
    } else {
      await store.dispatch('user/updateUser', {
        id: currentUser.value.id,
        data: userData
      })
      ElMessage.success('更新成功')
    }

    dialogVisible.value = false
    fetchUsers()

  } catch (error) {
    ElMessage.error(dialogMode.value === 'create' ? '创建失败' : '更新失败')
  }
}

const handlePageChange = (page) => {
  pagination.current = page
  fetchUsers()
}

// 生命周期
onMounted(() => {
  fetchUsers()
})
</script>

<style lang="scss" scoped>
.user-page {
  padding: 24px;

  .el-card {
    margin-bottom: 16px;
  }
}
</style>
