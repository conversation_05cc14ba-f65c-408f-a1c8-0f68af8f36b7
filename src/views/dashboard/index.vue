<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="24">
        <h1 class="dashboard__title">数据看板</h1>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24">
        <el-card title="销售趋势" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>销售趋势</span>
            </div>
          </template>
          <LineChart />
        </el-card>
      </el-col>
      <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24">
        <el-card title="产品分布" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>产品分布</span>
            </div>
          </template>
          <PieChart />
        </el-card>
      </el-col>
    </el-row>

    <!-- 表格区域 -->
    <el-row :gutter="20" class="table-row">
      <el-col :span="24">
        <el-card title="最新订单" class="table-card">
          <template #header>
            <div class="card-header">
              <span>最新订单</span>
            </div>
          </template>
          <DataTable />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import LineChart from './components/LineChart.vue'
import PieChart from './components/PieChart.vue'
import DataTable from './components/DataTable.vue'
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/colors' as *;
@use '@/styles/abstracts/mixins' as *;

.dashboard {
  padding: 24px;

  &__title {
    margin-bottom: 24px;
    color: $text-color-primary;
    font-weight: 600;
    font-size: 28px;
  }

  .charts-row {
    margin-bottom: 24px;
  }

  .chart-card,
  .table-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 0;

      span {
        font-size: 16px;
        font-weight: 600;
        color: $text-color-primary;
      }
    }
  }
}
</style>
