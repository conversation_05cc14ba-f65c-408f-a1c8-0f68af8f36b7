<template>
  <div class="terms-container">
    <h1>Terms of Service</h1>
    <div class="content">
      <p class="last-updated">Last Updated: June 30, 2025</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TermsView'
}
</script>

<style scoped>
.terms-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
}

h1 {
  color: var(--el-color-primary);
  margin-bottom: 20px;
}

.last-updated {
  font-style: italic;
  color: #909399;
  margin-bottom: 30px;
}

section {
  margin-bottom: 30px;
}

h2 {
  margin-bottom: 15px;
  color: var(--el-color-primary-dark-2);
}

.content {
  line-height: 1.6;
}

p {
  margin-bottom: 15px;
}

ul {
  margin-left: 20px;
  margin-bottom: 20px;
}

li {
  margin-bottom: 8px;
}
</style>
