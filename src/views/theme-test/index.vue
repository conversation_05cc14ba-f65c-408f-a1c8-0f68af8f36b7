<template>
  <div class="theme-test-container">
    <h1>Element Plus 主题覆盖测试</h1>
    
    <section>
      <h2>按钮 (Button)</h2>
      <div class="component-demo">
        <el-row class="mb-4">
          <el-button>默认按钮</el-button>
          <el-button type="primary">主要按钮</el-button>
          <el-button type="success">成功按钮</el-button>
          <el-button type="warning">警告按钮</el-button>
          <el-button type="danger">危险按钮</el-button>
        </el-row>
        
        <el-row class="mb-4">
          <el-button plain>朴素按钮</el-button>
          <el-button type="primary" plain>主要按钮</el-button>
          <el-button type="success" plain>成功按钮</el-button>
          <el-button type="warning" plain>警告按钮</el-button>
          <el-button type="danger" plain>危险按钮</el-button>
        </el-row>
        
        <el-row class="mb-4">
          <el-button disabled>禁用按钮</el-button>
          <el-button type="primary" disabled>主要按钮</el-button>
        </el-row>
      </div>
    </section>
    
    <section>
      <h2>表格 (Table)</h2>
      <div class="component-demo">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="date" label="日期" width="180" />
          <el-table-column prop="name" label="姓名" width="180" />
          <el-table-column prop="address" label="地址" />
        </el-table>
      </div>
    </section>
    
    <section>
      <h2>表单控件</h2>
      <div class="component-demo">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-input v-model="input" placeholder="请输入内容" />
          </el-col>
          <el-col :span="12">
            <el-select v-model="select" placeholder="请选择">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <el-checkbox v-model="checked">勾选</el-checkbox>
            <el-checkbox v-model="checked" disabled>禁用</el-checkbox>
          </el-col>
          <el-col :span="12">
            <el-radio-group v-model="radio">
              <el-radio :label="1">选项1</el-radio>
              <el-radio :label="2">选项2</el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
      </div>
    </section>
    
    <section>
      <h2>卡片 (Card)</h2>
      <div class="component-demo">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>卡片标题</span>
              <el-button class="button" type="text">操作按钮</el-button>
            </div>
          </template>
          <div v-for="o in 4" :key="o" class="text item">
            列表内容 {{ o }}
          </div>
        </el-card>
      </div>
    </section>
    
    <section>
      <h2>标签 (Tag)</h2>
      <div class="component-demo">
        <el-tag>标签一</el-tag>
        <el-tag type="success">标签二</el-tag>
        <el-tag type="warning">标签三</el-tag>
        <el-tag type="danger">标签四</el-tag>
        <el-tag type="info">标签五</el-tag>
      </div>
    </section>

    <section>
      <h2>对话框 (Dialog)</h2>
      <div class="component-demo">
        <el-button type="primary" @click="dialogVisible = true">
          打开对话框
        </el-button>
        <el-dialog
          v-model="dialogVisible"
          title="提示"
          width="30%">
          <span>这是一段对话框内容</span>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="dialogVisible = false">取消</el-button>
              <el-button type="primary" @click="dialogVisible = false">
                确认
              </el-button>
            </span>
          </template>
        </el-dialog>
      </div>
    </section>
    
    <section>
      <h2>CSS 变量测试</h2>
      <div class="component-demo">
        <div class="color-block primary">主色调</div>
        <div class="color-block success">成功色</div>
        <div class="color-block warning">警告色</div>
        <div class="color-block danger">危险色</div>
        <div class="color-block info">信息色</div>
      </div>
    </section>
    
    <section>
      <h2>主题覆盖说明</h2>
      <p>
        我们使用了 CSS 变量技术覆盖了 Element Plus 的默认主题。根据 
        <a href="https://element-plus.org/zh-CN/guide/theming.html" target="_blank">Element Plus 官方文档</a>，
        CSS 变量是一种非常有用的功能，已经被几乎所有浏览器所支持。
      </p>
      <p>
        通过在 <code>:root</code> 选择器中定义 CSS 变量，我们可以全局覆盖 Element Plus 的主题变量。
        这些变量以 <code>--el-</code> 开头，并在组件中使用。
      </p>
      <pre class="code-block">
:root {
  --el-color-primary: var(--color-brand-primary);
  --el-color-success: var(--color-functional-success);
  --el-text-color-primary: var(--color-text-primary);
  /* 更多变量... */
}
      </pre>
    </section>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 表格数据
const tableData = [
  {
    date: '2025-06-25',
    name: '张三',
    address: '北京市朝阳区',
  },
  {
    date: '2025-06-24',
    name: '李四',
    address: '上海市浦东新区',
  },
  {
    date: '2025-06-23',
    name: '王五',
    address: '广州市天河区',
  },
]

// 表单数据
const input = ref('')
const select = ref('')
const options = [
  {
    value: '选项1',
    label: '黄金糕',
  },
  {
    value: '选项2',
    label: '双皮奶',
  },
  {
    value: '选项3',
    label: '蚵仔煎',
  },
]
const checked = ref(true)
const radio = ref(1)

// 对话框
const dialogVisible = ref(false)
</script>

<style lang="scss" scoped>
.theme-test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  
  h1 {
    margin-bottom: 30px;
  }
  
  section {
    margin-bottom: 40px;
    
    h2 {
      margin-bottom: 20px;
      border-bottom: 1px solid var(--el-border-color);
      padding-bottom: 10px;
    }
    
    .component-demo {
      background-color: #fff;
      border-radius: 4px;
      padding: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      
      .mb-4 {
        margin-bottom: 16px;
      }
      
      .el-button {
        margin-right: 10px;
      }
      
      .el-tag {
        margin-right: 10px;
      }
    }
    
    .code-block {
      background-color: #f5f7fa;
      padding: 15px;
      border-radius: 4px;
      font-family: monospace;
      white-space: pre-wrap;
    }
    
    .color-block {
      display: inline-block;
      width: 100px;
      height: 100px;
      margin-right: 15px;
      border-radius: 4px;
      text-align: center;
      line-height: 100px;
      color: white;
      
      &.primary {
        background-color: var(--el-color-primary);
      }
      
      &.success {
        background-color: var(--el-color-success);
      }
      
      &.warning {
        background-color: var(--el-color-warning);
      }
      
      &.danger {
        background-color: var(--el-color-danger);
      }
      
      &.info {
        background-color: var(--el-color-info);
      }
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }
}
</style>
