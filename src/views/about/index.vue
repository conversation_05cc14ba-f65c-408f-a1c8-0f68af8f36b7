<template>
  <div class="about-container">
    <h1>About Us</h1>
    <div class="content">
      <p>Galaxy Vue Demi is a powerful Vue component library designed to work seamlessly with both Vue 2 and Vue 3.</p>
      <p>Our mission is to provide developers with a consistent and beautiful set of components that can be used across Vue versions, making migration and compatibility easier than ever.</p>
      
      <h2>Our Team</h2>
      <p>We are a team of passionate developers dedicated to creating high-quality, reusable components for the Vue ecosystem.</p>
      
      <h2>Features</h2>
      <ul>
        <li>Compatible with Vue 2 and Vue 3</li>
        <li>Comprehensive component library</li>
        <li>Customizable themes</li>
        <li>Responsive design</li>
        <li>Accessibility focused</li>
        <li>Well-documented API</li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AboutView'
}
</script>

<style scoped>
.about-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
}

h1 {
  color: var(--el-color-primary);
  margin-bottom: 30px;
}

h2 {
  margin-top: 30px;
  margin-bottom: 15px;
  color: var(--el-color-primary-dark-2);
}

.content {
  line-height: 1.6;
}

p {
  margin-bottom: 20px;
}

ul {
  margin-left: 20px;
  margin-bottom: 20px;
}

li {
  margin-bottom: 8px;
}
</style>
