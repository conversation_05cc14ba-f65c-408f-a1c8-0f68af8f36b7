<template>
  <div class="unauthorized-container">
    <div class="error-content">
      <h1>401</h1>
      <h2>Unauthorized Access</h2>
      <p>Sorry, you don't have permission to access this page.</p>
      <router-link to="/" class="back-btn">Back to Home</router-link>
    </div>
  </div>
</template>

<script setup>
// 使用组合式API
</script>

<style scoped>
.unauthorized-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  text-align: center;
}

.error-content {
  max-width: 500px;
  padding: 40px;
}

h1 {
  font-size: 6rem;
  margin: 0;
  color: var(--el-color-danger);
}

h2 {
  font-size: 2rem;
  margin: 10px 0 20px;
  color: var(--el-text-color-primary);
}

p {
  color: var(--el-text-color-secondary);
  margin-bottom: 30px;
}

.back-btn {
  display: inline-block;
  padding: 10px 20px;
  background-color: var(--el-color-primary);
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.back-btn:hover {
  background-color: var(--el-color-primary-dark-1);
}
</style>
