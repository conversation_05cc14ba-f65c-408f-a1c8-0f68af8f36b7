<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-form">
        <div class="login-header">
          <h1 class="login-title">登录</h1>
          <p class="login-subtitle">欢迎回来，请登录您的账户</p>
        </div>

        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="top"
          size="large"
          @submit.prevent="handleSubmit">
          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="form.email"
              type="email"
              placeholder="请输入邮箱"
              :prefix-icon="Message"
              clearable />
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              clearable />
          </el-form-item>

          <el-form-item>
            <div class="form-options">
              <el-checkbox v-model="form.rememberMe">
                记住我
              </el-checkbox>
              <el-link type="primary" @click="handleForgotPassword">
                忘记密码？
              </el-link>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              native-type="submit"
              style="width: 100%"
              @click="handleSubmit">
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>

        <div class="login-footer">
          <p>
            还没有账户？
            <el-link type="primary" @click="$router.push('/auth/register')">
              立即注册
            </el-link>
          </p>
        </div>

        <!-- 第三方登录 -->
        <div class="social-login">
          <el-divider>或使用以下方式登录</el-divider>
          <div class="social-buttons">
            <el-button
              circle
              title="GitHub 登录"
              @click="handleSocialLogin('github')">
              <SvgIcon name="github" />
            </el-button>
            <el-button
              circle
              title="Google 登录"
              @click="handleSocialLogin('google')">
              <SvgIcon name="google" />
            </el-button>
          </div>
        </div>
      </div>

      <!-- 侧边图片 -->
      <div class="login-banner">
        <div class="banner-content">
          <h2>Galaxy Vue Demi</h2>
          <p>现代化的前端开发框架，助力您快速构建优秀的Web应用</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Message, Lock } from '@element-plus/icons-vue'
import { useAuth } from '@/composables/useAuth'
import SvgIcon from '@/components/base/svg-icon/index.vue'

const router = useRouter()
const { login } = useAuth()

const formRef = ref()
const loading = ref(false)

const form = reactive({
  email: '',
  password: '',
  rememberMe: false
})

const rules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    const success = await login({
      email: form.email,
      password: form.password,
      rememberMe: form.rememberMe
    })

    if (success) {
      ElMessage.success('登录成功')
      // 登录成功后会自动跳转，由 useAuth 处理
    }

  } catch (error) {
    ElMessage.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

const handleForgotPassword = () => {
  router.push('/auth/forgot-password')
}

const handleSocialLogin = (provider) => {
  ElMessage.info(`${provider} 登录功能开发中`)
}
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/colors' as *;
@use '@/styles/abstracts/mixins' as *;

.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, $color-brand-primary 0%, $color-functional-primary 100%);
  padding: 20px;
}

.login-container {
  display: flex;
  background: $bg-color-white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 900px;
  width: 100%;
  min-height: 600px;

  @include respond-to('tablet') {
    flex-direction: column;
    max-width: 400px;
  }
}

.login-form {
  flex: 1;
  padding: 48px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  @include respond-to('tablet') {
    padding: 32px 24px;
  }
}

.login-header {
  text-align: center;
  margin-bottom: 32px;

  .login-title {
    font-size: 28px;
    font-weight: 700;
    color: $text-color-primary;
    margin-bottom: 8px;
  }

  .login-subtitle {
    color: $text-color-secondary;
    font-size: 14px;
  }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-footer {
  text-align: center;
  margin-top: 24px;

  p {
    color: $text-color-secondary;
    font-size: 14px;
  }
}

.social-login {
  margin-top: 24px;

  .social-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 16px;
  }
}

.login-banner {
  flex: 1;
  background: linear-gradient(135deg, $color-border-base 0%, $color-fill-emphasis 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: #fff;
  text-align: center;

  @include respond-to('tablet') {
    display: none;
  }

  .banner-image {
    width: 300px;
    height: auto;
    margin-bottom: 32px;
  }

  .banner-content {
    h2 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 16px;
    }

    p {
      font-size: 16px;
      opacity: 0.9;
      line-height: 1.6;
    }
  }
}

:deep(.el-form-item__label) {
  color: $text-color-primary;
  font-weight: 500;
}

:deep(.el-input__inner) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
}
</style>
