<template>
  <div class="typography-example">
    <div class="example-preview">
      <p
        :style="{
          fontSize: example.size,
          lineHeight: example.lineHeight
        }" class="example-text">{{ example.text }}</p>
    </div>
    <div class="example-info">
      <div class="example-name-value">
        <span class="example-name">{{ example.name }}</span>
        <span class="example-value">{{ example.size }}</span>
      </div>
      <div class="example-lineheight">{{ example.lineHeight }}</div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  example: {
    type: Object,
    required: true,
    validator: (prop) => {
      return prop.name && prop.size && prop.lineHeight && prop.text
    }
  }
})
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/_colors.scss' as *;
@use '@/styles/base/_typography.scss' as *;

.typography-example {
  background: white;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  border: none;

  .example-preview {
    padding: 12px;
    margin-bottom: 8px;
    background: white;
    border: 1px solid $color-border-base;

    .example-text {
      margin: 0;
      color: $color-text-primary;
    }
  }

  .example-info {
    .example-name-value {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
    }

    .example-name {
      font-size: 14px;
      color: $color-text-primary;
      font-weight: 500;
    }

    .example-value {
      font-size: 14px;
      color: $color-text-secondary;
    }

    .example-lineheight {
      font-size: 12px;
      color: $color-text-tertiary;
    }
  }
}
</style>
