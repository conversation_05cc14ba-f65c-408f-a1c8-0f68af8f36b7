<template>
  <div class="navigation-examples">
    <el-row :gutter="24">
      <!-- 标签页 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="component-container">
          <h4>标签页</h4>
          <el-tabs v-model="activeTab" class="demo-tabs">
            <el-tab-pane label="标签页1" name="first">标签页1内容</el-tab-pane>
            <el-tab-pane label="标签页2" name="second">标签页2内容</el-tab-pane>
            <el-tab-pane label="标签页3" name="third">标签页3内容</el-tab-pane>
            <el-tab-pane label="标签页4" name="fourth">标签页4内容</el-tab-pane>
          </el-tabs>
          <div class="component-description">
            标签页组件，用于在同一块区域内切换不同内容
          </div>
        </div>
      </el-col>

      <!-- 卡片式标签页 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="component-container">
          <h4>卡片式标签页</h4>
          <el-tabs v-model="activeCardTab" type="card" class="demo-tabs">
            <el-tab-pane label="卡片1" name="first">卡片1内容</el-tab-pane>
            <el-tab-pane label="卡片2" name="second">卡片2内容</el-tab-pane>
            <el-tab-pane label="卡片3" name="third">卡片3内容</el-tab-pane>
            <el-tab-pane label="卡片4" name="fourth">卡片4内容</el-tab-pane>
          </el-tabs>
          <div class="component-description">
            卡片式标签页，具有卡片化的外观
          </div>
        </div>
      </el-col>

      <!-- 面包屑 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="component-container">
          <h4>面包屑</h4>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item><a href="/">设计系统</a></el-breadcrumb-item>
            <el-breadcrumb-item>组件示例</el-breadcrumb-item>
            <el-breadcrumb-item>导航组件</el-breadcrumb-item>
          </el-breadcrumb>
          <div class="component-description">
            面包屑组件，显示当前页面在系统层级结构中的位置
          </div>
        </div>
      </el-col>

      <!-- 分页 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="component-container">
          <h4>分页</h4>
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 30, 40]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
          <div class="component-description">
            分页组件，用于数据量较大时进行分页展示
          </div>
        </div>
      </el-col>

      <!-- 步骤条 -->
      <el-col :xs="24">
        <div class="component-container">
          <h4>步骤条</h4>
          <el-steps :active="activeStep" finish-status="success">
            <el-step title="步骤 1" description="这是步骤1的描述信息" />
            <el-step title="步骤 2" description="这是步骤2的描述信息" />
            <el-step title="步骤 3" description="这是步骤3的描述信息" />
          </el-steps>
          <div class="step-controls">
            <el-button type="primary" :disabled="activeStep === 0" @click="prevStep">上一步</el-button>
            <el-button type="primary" :disabled="activeStep === 3" @click="nextStep">下一步</el-button>
          </div>
          <div class="component-description">
            步骤条组件，引导用户按照流程完成任务
          </div>
        </div>
      </el-col>

      <!-- 下拉菜单 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>下拉菜单</h4>
          <el-dropdown>
            <el-button type="primary">
              下拉菜单<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>选项1</el-dropdown-item>
                <el-dropdown-item>选项2</el-dropdown-item>
                <el-dropdown-item>选项3</el-dropdown-item>
                <el-dropdown-item disabled>选项4</el-dropdown-item>
                <el-dropdown-item divided>选项5</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <div class="component-description">
            下拉菜单，点击触发按钮显示更多操作
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'

// 标签页
const activeTab = ref('first')
const activeCardTab = ref('first')

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

const handleSizeChange = (val) => {
  console.log(`每页 ${val} 条`)
}

const handleCurrentChange = (val) => {
  console.log(`当前页: ${val}`)
}

// 步骤条
const activeStep = ref(0)

const nextStep = () => {
  if (activeStep.value < 3) {
    activeStep.value++
  }
}

const prevStep = () => {
  if (activeStep.value > 0) {
    activeStep.value--
  }
}
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/_colors.scss' as *;
@use '@/styles/base/_typography.scss' as *;

.navigation-examples {
  .component-container {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid $color-border-base;
    border-radius: 4px;
    background-color: #ffffff;

    h4 {
      @include heading-2;
      font-size: 16px;
      margin-top: 0;
      margin-bottom: 12px;
      color: $color-text-primary;
    }

    .component-description {
      margin-top: 12px;
      @include small-text;
      color: $color-text-secondary;
    }

    .step-controls {
      margin-top: 16px;
      display: flex;
      gap: 8px;
    }

    .demo-tabs {
      margin-bottom: 16px;
    }
  }
}
</style>
