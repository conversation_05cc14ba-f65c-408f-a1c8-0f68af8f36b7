<template>
  <div class="data-display-examples">
    <el-row :gutter="24">
      <!-- 标签 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>标签</h4>
          <div class="tag-examples">
            <el-tag>标签一</el-tag>
            <el-tag type="success">成功标签</el-tag>
            <el-tag type="info">信息标签</el-tag>
            <el-tag type="warning">警告标签</el-tag>
            <el-tag type="danger">危险标签</el-tag>
          </div>
          <div class="component-description">
            标签组件，用于标记和选择
          </div>
        </div>
      </el-col>

      <!-- 可移除标签 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>可移除标签</h4>
          <div class="tag-examples">
            <el-tag
              v-for="tag in dynamicTags"
              :key="tag"
              class="mx-1"
              closable
              :disable-transitions="false"
              @close="handleClose(tag)">
              {{ tag }}
            </el-tag>
            <el-input
              v-if="inputVisible"
              ref="InputRef"
              v-model="inputValue"
              class="ml-1 w-20"
              size="small"
              @keyup.enter="handleInputConfirm"
              @blur="handleInputConfirm" />
            <el-button v-else class="button-new-tag ml-1" size="small" @click="showInput">
              + 新标签
            </el-button>
          </div>
          <div class="component-description">
            可动态添加和删除的标签组
          </div>
        </div>
      </el-col>

      <!-- 徽章 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>徽章</h4>
          <div class="badge-examples">
            <el-badge :value="12" class="item">
              <el-button>评论</el-button>
            </el-badge>
            <el-badge :value="3" class="item" type="primary">
              <el-button>回复</el-button>
            </el-badge>
            <el-badge :value="1" class="item" type="warning">
              <el-button>消息</el-button>
            </el-badge>
            <el-badge :value="2" class="item" type="danger">
              <el-button>通知</el-button>
            </el-badge>
          </div>
          <div class="component-description">
            徽章组件，出现在按钮、图标旁的数字或状态标记
          </div>
        </div>
      </el-col>

      <!-- 卡片 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>卡片</h4>
          <el-card class="box-card">
            <template #header>
              <div class="card-header">
                <span>卡片标题</span>
                <el-button class="button" text>操作按钮</el-button>
              </div>
            </template>
            <div v-for="o in 4" :key="o" class="text item">
              {{ '列表内容 ' + o }}
            </div>
          </el-card>
          <div class="component-description">
            卡片组件，将信息聚合在卡片容器中展示
          </div>
        </div>
      </el-col>

      <!-- 折叠面板 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>折叠面板</h4>
          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="一致性 Consistency" name="1">
              <div>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念</div>
              <div>在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等</div>
            </el-collapse-item>
            <el-collapse-item title="反馈 Feedback" name="2">
              <div>控制反馈：通过界面样式和交互动效让用户可以清晰的感知自己的操作</div>
              <div>页面反馈：操作后，通过页面元素的变化清晰地展现当前状态</div>
            </el-collapse-item>
            <el-collapse-item title="效率 Efficiency" name="3">
              <div>简化流程：设计简洁直观的操作流程</div>
              <div>清晰明确：语言表达清晰且表意明确，让用户快速理解进而作出决策</div>
              <div>帮助用户识别：界面简单直白，让用户快速识别而非回忆，减少用户记忆负担</div>
            </el-collapse-item>
          </el-collapse>
          <div class="component-description">
            折叠面板组件，通过折叠收纳内容区域
          </div>
        </div>
      </el-col>

      <!-- 文字提示 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>文字提示</h4>
          <div class="tooltip-examples">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="Top 提示文字"
              placement="top">
              <el-button>上方</el-button>
            </el-tooltip>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="Bottom 提示文字"
              placement="bottom">
              <el-button>下方</el-button>
            </el-tooltip>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="Left 提示文字"
              placement="left">
              <el-button>左侧</el-button>
            </el-tooltip>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="Right 提示文字"
              placement="right">
              <el-button>右侧</el-button>
            </el-tooltip>
          </div>
          <div class="component-description">
            文字提示组件，鼠标悬停时显示提示信息
          </div>
        </div>
      </el-col>

      <!-- 气泡卡片 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>气泡卡片</h4>
          <div class="popover-examples">
            <el-popover
              placement="top-start"
              title="标题"
              :width="200"
              trigger="hover"
              content="这是一段内容,这是一段内容,这是一段内容,这是一段内容。">
              <template #reference>
                <el-button>hover 激活</el-button>
              </template>
            </el-popover>

            <el-popover
              placement="bottom"
              title="标题"
              :width="200"
              trigger="click"
              content="这是一段内容,这是一段内容,这是一段内容,这是一段内容。">
              <template #reference>
                <el-button>click 激活</el-button>
              </template>
            </el-popover>
          </div>
          <div class="component-description">
            气泡卡片组件，鼠标点击/悬停时显示更多内容
          </div>
        </div>
      </el-col>

      <!-- 头像 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>头像</h4>
          <div class="avatar-examples">
            <div class="avatar-row">
              <el-avatar :size="50" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
              <el-avatar :size="50" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" />
              <el-avatar :size="50" icon="UserFilled" />
            </div>
            <div class="avatar-row">
              <el-avatar size="small" icon="UserFilled" />
              <el-avatar icon="UserFilled" />
              <el-avatar size="large" icon="UserFilled" />
            </div>
          </div>
          <div class="component-description">
            头像组件，用于展示用户头像
          </div>
        </div>
      </el-col>

      <!-- 空状态 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>空状态</h4>
          <el-empty description="暂无数据" />
          <div class="component-description">
            空状态组件，空数据时的占位提示
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'

// 标签
const dynamicTags = ref(['标签一', '标签二', '标签三'])
const inputVisible = ref(false)
const inputValue = ref('')
const InputRef = ref()

const handleClose = (tag) => {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1)
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    if (InputRef.value) InputRef.value.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value) {
    dynamicTags.value.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 折叠面板
const activeCollapse = ref(['1'])
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/_colors.scss' as *;
@use '@/styles/base/_typography.scss' as *;

.data-display-examples {
  .component-container {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid $color-border-base;
    border-radius: 4px;
    background-color: #ffffff;

    h4 {
      @include heading-2;
      font-size: 16px;
      margin-top: 0;
      margin-bottom: 12px;
      color: $color-text-primary;
    }

    .component-description {
      margin-top: 12px;
      @include small-text;
      color: $color-text-secondary;
    }

    .tag-examples {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      align-items: center;
    }

    .badge-examples {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;

      .item {
        margin-right: 8px;
      }
    }

    .box-card {
      width: 100%;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .text {
        font-size: 14px;
      }

      .item {
        margin-bottom: 10px;
      }
    }

    .tooltip-examples,
    .popover-examples {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
    }

    .avatar-examples {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .avatar-row {
        display: flex;
        gap: 16px;
        align-items: center;
      }
    }
  }
}
</style>
