<template>
  <div>
    <h3>数据表格示例</h3>
    <el-table :data="tableData" border style="width: 100%" class="data-table">
      <!-- 状态列 -->
      <el-table-column prop="status" label="状态" width="120">
        <template #default="{ row }">
          <div class="status-cell">
            <span v-if="row.status === 'unread'" class="status-dot"></span>
            <span>{{ row.status === 'unread' ? '未读' : '已读' }}</span>
          </div>
        </template>
      </el-table-column>

      <!-- 名称列 -->
      <el-table-column prop="name" label="名称" width="120">
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>

      <!-- 描述列 -->
      <el-table-column prop="description" label="描述" width="120">
        <template #default="{ row }">
          <span>{{ row.description }}</span>
        </template>
      </el-table-column>

      <!-- 时间列 -->
      <el-table-column prop="time" label="时间" width="180">
        <template #default="{ row }">
          <span>{{ row.time }}</span>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="240">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="default" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    required: true,
    default: () => []
  }
})

const emit = defineEmits(['view', 'edit', 'delete'])

// 使用传入的数据或默认数据
const tableData = props.data.length > 0 ? props.data : [
  {
    name: '品牌色主色',
    description: '主要的品牌色彩定义',
    status: 'unread',
    time: '2024-04-21 16:02:30'
  },
  {
    name: '功能色',
    description: '不同功能状态下使用的颜色',
    status: 'read',
    time: '2024-04-21 15:45:12'
  },
  {
    name: '字体颜色',
    description: '不同层级和用途的文字颜色',
    status: 'unread',
    time: '2024-04-21 14:30:45'
  },
  {
    name: '基础颜色',
    description: '描边色和填充色',
    status: 'read',
    time: '2024-04-21 13:15:22'
  },
  {
    name: '字体规范',
    description: '不同场景下的字体大小和行高标准',
    status: 'unread',
    time: '2024-04-21 12:05:18'
  }
]

// 操作按钮的处理函数
const handleView = (row) => {
  emit('view', row)
}

const handleEdit = (row) => {
  emit('edit', row)
}

const handleDelete = (row) => {
  emit('delete', row)
}
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/_colors.scss' as *;

.data-table {
  :deep(.el-table__header) {
    background-color: var(--color-brand-table-header, $color-brand-table-header);

    th {
      background-color: var(--color-brand-table-header, $color-brand-table-header);
      color: var(--color-text-primary, $color-text-primary);
      font-weight: 600;
      padding: 12px 16px;
    }
  }

  :deep(.el-table__body) {
    tr:nth-child(odd) {
      background-color: #ffffff;
    }

    tr:nth-child(even) {
      background-color: var(--color-fill-table-bg, $color-fill-table-bg);
    }

    tr:hover > td {
      background-color: var(--color-brand-tag-bg, $color-brand-tag-bg) !important;
    }

    tr.current-row > td {
      background-color: var(--color-brand-tag-bg, $color-brand-tag-bg) !important;
      color: var(--color-brand-primary, $color-brand-primary);
    }

    td {
      padding: 12px 16px;
      color: var(--color-text-primary, $color-text-primary);
      border-color: var(--color-border-base, $color-border-base);
    }
  }

  :deep(.el-table__empty-block) {
    background-color: #ffffff;

    .el-table__empty-text {
      color: var(--color-text-secondary, $color-text-secondary);
    }
  }

  .status-cell {
    display: flex;
    align-items: center;

    .status-dot {
      width: 8px;
      height: 8px;
      background-color: var(--color-functional-primary, $color-functional-primary);
      border-radius: 50%;
      display: inline-block;
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }
}
</style>
