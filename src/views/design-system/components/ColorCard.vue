<template>
  <div class="color-card">
    <div class="color-preview" :style="{ backgroundColor: color.value }"></div>
    <div class="color-info">
      <div class="color-name-value">
        <span class="color-name">{{ color.name }}</span>
        <span class="color-value">{{ color.value }}</span>
      </div>
      <div v-if="color.scss" class="scss-value">{{ color.scss }}</div>
      <div v-if="color.description" class="color-description">{{ color.description }}</div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  color: {
    type: Object,
    required: true,
    validator: (prop) => {
      return prop.name && prop.value && prop.description !== undefined
    }
  }
})
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/_colors.scss' as *;
@use '@/styles/base/_typography.scss' as *;

.color-card {
  background: white;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  border: none;

  .color-preview {
    width: 100%;
    height: 80px;
    margin-bottom: 8px;
  }

  .color-info {
    .color-name-value {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
    }

    .color-name {
      font-size: 14px;
      color: $color-text-primary;
      font-weight: 500;
    }

    .color-value {
      font-size: 14px;
      color: $color-text-secondary;
    }

    .scss-value {
      font-size: 12px;
      color: $color-text-tertiary;
      margin-bottom: 4px;
    }

    .color-description {
      font-size: 12px;
      color: $color-text-secondary;
      margin-top: 4px;
    }
  }
}
</style>
