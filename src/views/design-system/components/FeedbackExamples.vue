<template>
  <div class="feedback-examples">
    <el-row :gutter="24">
      <!-- 提示框 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="component-container">
          <h4>提示框</h4>
          <div class="alert-examples">
            <el-alert title="成功提示" type="success" show-icon />
            <el-alert title="警告提示" type="warning" show-icon />
            <el-alert title="错误提示" type="error" show-icon />
            <el-alert title="信息提示" type="info" show-icon />
          </div>
          <div class="component-description">
            提示框组件，用于页面中展示重要的提示信息
          </div>
        </div>
      </el-col>

      <!-- 带描述的提示框 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="component-container">
          <h4>带描述的提示框</h4>
          <el-alert
            title="成功提示的文案"
            type="success"
            description="文字说明文字说明文字说明文字说明文字说明文字说明"
            show-icon />
          <div class="component-description">
            带有辅助性文字介绍的提示框
          </div>
        </div>
      </el-col>

      <!-- 消息提示 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>消息提示</h4>
          <div class="button-group">
            <el-button @click="openMessage('success')">成功</el-button>
            <el-button @click="openMessage('warning')">警告</el-button>
            <el-button @click="openMessage('info')">消息</el-button>
            <el-button @click="openMessage('error')">错误</el-button>
          </div>
          <div class="component-description">
            消息提示组件，从顶部出现，自动消失
          </div>
        </div>
      </el-col>

      <!-- 通知 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>通知</h4>
          <div class="button-group">
            <el-button @click="openNotification('success')">成功</el-button>
            <el-button @click="openNotification('warning')">警告</el-button>
            <el-button @click="openNotification('info')">消息</el-button>
            <el-button @click="openNotification('error')">错误</el-button>
          </div>
          <div class="component-description">
            通知组件，从右侧出现，可自定义关闭时间
          </div>
        </div>
      </el-col>

      <!-- 对话框 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>对话框</h4>
          <div class="button-group">
            <el-button type="primary" @click="dialogVisible = true">打开对话框</el-button>
          </div>
          <el-dialog
            v-model="dialogVisible"
            title="对话框标题"
            width="30%"
            :before-close="handleClose">
            <span>这是一段对话框的内容</span>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="dialogVisible = false">确认</el-button>
              </span>
            </template>
          </el-dialog>
          <div class="component-description">
            对话框组件，在保留当前页面状态的情况下，告知用户并承载相关操作
          </div>
        </div>
      </el-col>

      <!-- 加载中 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>加载中</h4>
          <div class="loading-examples">
            <div class="loading-item">
              <el-button @click="showFullScreenLoading">全屏加载</el-button>
            </div>
            <div class="loading-item">
              <div v-loading="loading" class="loading-content">
                <span>局部加载示例区域</span>
              </div>
              <el-button @click="toggleLoading">{{ loading ? '关闭' : '打开' }}局部加载</el-button>
            </div>
          </div>
          <div class="component-description">
            加载中组件，在加载过程中展示动画效果
          </div>
        </div>
      </el-col>

      <!-- 进度条 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>进度条</h4>
          <div class="progress-examples">
            <el-progress :percentage="50" />
            <el-progress :percentage="80" color="#8e71c7" />
            <el-progress :percentage="100" status="success" />
            <el-progress :percentage="50" status="exception" />
          </div>
          <div class="component-description">
            进度条组件，用于展示操作进度
          </div>
        </div>
      </el-col>

      <!-- 抽屉 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>抽屉</h4>
          <div class="button-group">
            <el-button type="primary" @click="drawer = true">打开抽屉</el-button>
          </div>
          <el-drawer v-model="drawer" title="抽屉标题" direction="rtl">
            <span>这是一段抽屉的内容</span>
          </el-drawer>
          <div class="component-description">
            抽屉组件，从屏幕边缘滑出的面板
          </div>
        </div>
      </el-col>

      <!-- 气泡确认框 -->
      <el-col :xs="24" :sm="12" :md="8">
        <div class="component-container">
          <h4>气泡确认框</h4>
          <el-popconfirm
            title="确定删除这条数据吗？"
            @confirm="confirmDelete"
            @cancel="cancelDelete">
            <template #reference>
              <el-button>删除</el-button>
            </template>
          </el-popconfirm>
          <div class="component-description">
            气泡确认框，点击元素，弹出气泡确认框
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElNotification, ElLoading } from 'element-plus'

// 消息提示
const openMessage = (type) => {
  ElMessage({
    type,
    message: `这是一条${type === 'success' ? '成功' : type === 'warning' ? '警告' : type === 'info' ? '消息' : '错误'}提示`,
  })
}

// 通知
const openNotification = (type) => {
  ElNotification({
    title: type === 'success' ? '成功' : type === 'warning' ? '警告' : type === 'info' ? '消息' : '错误',
    message: `这是一条${type === 'success' ? '成功' : type === 'warning' ? '警告' : type === 'info' ? '消息' : '错误'}通知`,
    type,
  })
}

// 对话框
const dialogVisible = ref(false)
const handleClose = (done) => {
  done()
}

// 加载中
const loading = ref(false)
const toggleLoading = () => {
  loading.value = !loading.value
}

const showFullScreenLoading = () => {
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  setTimeout(() => {
    loadingInstance.close()
  }, 2000)
}

// 抽屉
const drawer = ref(false)

// 气泡确认框
const confirmDelete = () => {
  ElMessage({
    type: 'success',
    message: '删除成功',
  })
}

const cancelDelete = () => {
  ElMessage({
    type: 'info',
    message: '已取消删除',
  })
}
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/_colors.scss' as *;
@use '@/styles/base/_typography.scss' as *;

.feedback-examples {
  .component-container {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid $color-border-base;
    border-radius: 4px;
    background-color: #ffffff;

    h4 {
      @include heading-2;
      font-size: 16px;
      margin-top: 0;
      margin-bottom: 12px;
      color: $color-text-primary;
    }

    .component-description {
      margin-top: 12px;
      @include small-text;
      color: $color-text-secondary;
    }

    .alert-examples {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .button-group {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .loading-examples {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .loading-item {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .loading-content {
        height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: $color-fill-table-bg;
        border-radius: 4px;
      }
    }

    .progress-examples {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  }
}
</style>
