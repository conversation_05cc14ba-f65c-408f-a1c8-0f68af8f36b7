<template>
  <div class="feature-grid">
    <h2 class="feature-grid__title">核心特性</h2>
    <div class="feature-grid__container">
      <div v-for="feature in features" :key="feature.id" class="feature-card">
        <div class="feature-card__icon">
          <el-icon :size="48">
            <component :is="feature.icon" />
          </el-icon>
        </div>
        <h3 class="feature-card__title">{{ feature.title }}</h3>
        <p class="feature-card__description">{{ feature.description }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Lightning, Setting, Lock, Monitor, Tools, Trophy } from '@element-plus/icons-vue'

const features = [
  {
    id: 1,
    icon: Lightning,
    title: '快速开发',
    description: '基于 Vite 的快速构建，热模块替换，提升开发效率',
  },
  {
    id: 2,
    icon: Setting,
    title: '灵活配置',
    description: '支持多种配置选项，可根据项目需求自由定制',
  },
  {
    id: 3,
    icon: Lock,
    title: '类型安全',
    description: '完整的 TypeScript 支持，提供更好的代码提示和错误检查',
  },
  {
    id: 4,
    icon: Monitor,
    title: '响应式设计',
    description: '移动端优先的响应式设计，适配各种设备屏幕',
  },
  {
    id: 5,
    icon: Tools,
    title: '工程化工具',
    description: '集成 ESLint、Prettier、Husky 等工程化工具',
  },
  {
    id: 6,
    icon: Trophy,
    title: '最佳实践',
    description: '遵循业界最佳实践，提供高质量的代码结构',
  },
]
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/colors' as *;

.feature-grid {
  padding: 80px 0;

  &__title {
    font-size: 36px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 48px;
    color: $text-color-primary;
  }

  &__container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
    max-width: 1200px;
    margin: 0 auto;
  }
}

.feature-card {
  padding: 32px;
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid $color-border-base;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: $color-primary;
  }

  &__icon {
    margin-bottom: 24px;
    color: $color-primary;
  }

  &__title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 16px;
    color: $text-color-primary;
  }

  &__description {
    font-size: 14px;
    line-height: 1.6;
    color: $text-color-regular;
  }
}
</style>
