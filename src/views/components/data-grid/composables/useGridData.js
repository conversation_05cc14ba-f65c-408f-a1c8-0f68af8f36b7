import { ref, computed } from 'vue'

/**
 * 数据网格组件的组合式函数
 * 处理数据网格相关的状态和操作
 */
export function useGridData() {
  // 表格数据
  const gridData = ref([
    { id: 1, name: '<PERSON>', email: '<EMAIL>', status: 'Active' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', status: 'Inactive' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', status: 'Pending' },
    { id: 4, name: '<PERSON>', email: '<EMAIL>', status: 'Active' },
    { id: 5, name: '<PERSON>', email: '<EMAIL>', status: 'Active' }
  ])

  // 筛选状态
  const filterStatus = ref('all')
  
  // 根据筛选条件计算显示的数据
  const filteredData = computed(() => {
    if (filterStatus.value === 'all') {
      return gridData.value
    }
    return gridData.value.filter(item => 
      item.status.toLowerCase() === filterStatus.value.toLowerCase()
    )
  })

  // 编辑项目
  const editItem = (id) => {
    // 实际应用中，这里可能会打开一个编辑对话框
    console.log(`Editing item with id: ${id}`)
  }

  // 删除项目
  const deleteItem = (id) => {
    // 实际应用中，可能会先显示确认对话框
    gridData.value = gridData.value.filter(item => item.id !== id)
    console.log(`Deleted item with id: ${id}`)
  }

  // 更改筛选条件
  const setFilter = (status) => {
    filterStatus.value = status
  }

  return {
    gridData,
    filteredData,
    filterStatus,
    editItem,
    deleteItem,
    setFilter
  }
}
