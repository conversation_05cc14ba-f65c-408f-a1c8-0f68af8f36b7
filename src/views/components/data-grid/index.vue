<template>
  <div class="data-grid-container">
    <h1>Data Grid Component</h1>
    <p>This is the data grid component demonstration page.</p>
    
    <!-- 筛选器 -->
    <div class="filter-container">
      <span class="filter-label">Filter by status:</span>
      <div class="filter-options">
        <button 
          :class="['filter-btn', filterStatus === 'all' ? 'active' : '']" 
          @click="setFilter('all')">
          All
        </button>
        <button 
          :class="['filter-btn', filterStatus === 'active' ? 'active' : '']" 
          @click="setFilter('active')">
          Active
        </button>
        <button 
          :class="['filter-btn', filterStatus === 'inactive' ? 'active' : '']" 
          @click="setFilter('inactive')">
          Inactive
        </button>
        <button 
          :class="['filter-btn', filterStatus === 'pending' ? 'active' : '']" 
          @click="setFilter('pending')">
          Pending
        </button>
      </div>
    </div>
    
    <div class="grid-demo">
      <table class="demo-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Email</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in filteredData" :key="item.id">
            <td>{{ item.id }}</td>
            <td>{{ item.name }}</td>
            <td>{{ item.email }}</td>
            <td>
              <span :class="['status-badge', item.status.toLowerCase()]">
                {{ item.status }}
              </span>
            </td>
            <td class="actions">
              <button class="action-btn edit" @click="editItem(item.id)">Edit</button>
              <button class="action-btn delete" @click="deleteItem(item.id)">Delete</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { useGridData } from './composables/useGridData'

// 使用组合式函数获取数据和方法
const { 
  filteredData, 
  filterStatus, 
  editItem, 
  deleteItem, 
  setFilter 
} = useGridData()
</script>

<style scoped>
.data-grid-container {
  padding: 20px;
}

h1 {
  margin-bottom: 20px;
  color: var(--el-color-primary);
}

/* 筛选器样式 */
.filter-container {
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-label {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.filter-options {
  display: flex;
  gap: 8px;
}

.filter-btn {
  padding: 6px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s;
}

.filter-btn.active {
  background-color: var(--el-color-primary);
  color: white;
  border-color: var(--el-color-primary);
}

.grid-demo {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.demo-table {
  width: 100%;
  border-collapse: collapse;
}

.demo-table th, .demo-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ebeef5;
}

.demo-table th {
  background-color: #f5f7fa;
  font-weight: bold;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-badge.active {
  background-color: #67c23a;
  color: white;
}

.status-badge.inactive {
  background-color: #909399;
  color: white;
}

.status-badge.pending {
  background-color: #e6a23c;
  color: white;
}

.actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.action-btn.edit {
  background-color: #409eff;
  color: white;
}

.action-btn.delete {
  background-color: #f56c6c;
  color: white;
}
</style>
