const CHINESE_CONSTANTS = {
  MAXIMUM_NUMBER: 999999999999.99,
  DIGITS: ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'],
  RADICES: ['', '拾', '佰', '仟'],
  BIG_RADICES: ['', '万', '亿'],
  DECIMALS: ['角', '分'],
  DOLLAR: '元',
  INTEGER: '整',
}

/**
 * 将数字转换为中文货币格式
 * @param {number|string} num - 要转换的数字
 * @returns {string} - 中文货币表示
 */
export function convertToChineseMoney(num) {
  if (num === null || num === undefined || num === '') return ''
  let currencyDigits = String(num)
  if (currencyDigits === '') return ''

  // 验证输入是否只包含数字、小数点和逗号
  if (currencyDigits.match(/[^,.\d]/)) return ''
  // 验证输入格式是否正确
  if (!currencyDigits.match(/^((\d{1,3}(,\d{3})*(.((\d{3},)*\d{1,3}))?)|(\d+(.\d+)?))$/)) return ''

  // 移除逗号和前导零
  currencyDigits = currencyDigits.replace(/,/g, '')
  currencyDigits = currencyDigits.replace(/^0+/, '')

  // 验证数字大小
  if (Number(currencyDigits) > CHINESE_CONSTANTS.MAXIMUM_NUMBER) {
    console.warn('输入的金额太大，请重新输入!')
    return ''
  }

  const parts = currencyDigits.split('.')
  // eslint-disable-next-line prefer-const
  let [integral, decimal = ''] = parts

  if (parts.length > 1) {
    decimal = decimal.substr(0, 2)
  }

  let result = ''

  if (Number(integral) > 0) {
    result = processIntegralPart(integral, CHINESE_CONSTANTS)
  } else if (Number(integral) === 0 && (decimal === '' || decimal === '0' || decimal === '00')) {
    // 零的特殊情况
    result = CHINESE_CONSTANTS.DIGITS[0] + CHINESE_CONSTANTS.DOLLAR
  }

  // 处理小数部分
  if (decimal !== '') {
    const hasIntegral = Number(integral) > 0
    result += processDecimalPart(decimal, CHINESE_CONSTANTS, hasIntegral)
  }

  // 处理特殊情况
  if (result === '') {
    result = CHINESE_CONSTANTS.DIGITS[0] + CHINESE_CONSTANTS.DOLLAR
  }

  // 添加"整"字
  if (decimal === '' || decimal === '00') {
    result += CHINESE_CONSTANTS.INTEGER
  }

  return result
}

/**
 * 处理整数部分的转换
 * @param {string} integral - 整数部分
 * @param {object} constants - 常量对象
 * @returns {string} - 中文表示
 */
function processIntegralPart(integral, constants) {
  let result = ''
  let zeroCount = 0

  for (let i = 0; i < integral.length; i++) {
    const position = integral.length - i - 1
    const digit = integral.charAt(i)
    const quotient = Math.floor(position / 4)
    const modulus = position % 4

    if (digit === '0') {
      zeroCount++
    } else {
      if (zeroCount > 0) {
        result += constants.DIGITS[0]
      }
      zeroCount = 0
      result += constants.DIGITS[Number(digit)] + constants.RADICES[modulus]
    }

    if (modulus === 0 && zeroCount < 4) {
      result += constants.BIG_RADICES[quotient]
    }
  }

  return result + constants.DOLLAR
}

/**
 * 处理小数部分的转换
 * @param {string} decimal - 小数部分
 * @param {object} constants - 常量对象
 * @param {boolean} hasIntegral - 是否有整数部分
 * @returns {string} - 中文表示
 */
function processDecimalPart(decimal, constants, hasIntegral = false) {
  if (decimal === '01') {
    // 对于 0.01（没有整数部分）- 直接返回壹分
    if (!hasIntegral) {
      return constants.DIGITS[1] + constants.DECIMALS[1]
    }
    // 对于 1.01（有整数部分）- 必须包含零
    return constants.DIGITS[0] + constants.DIGITS[1] + constants.DECIMALS[1]
  }

  let result = ''

  for (let i = 0; i < decimal.length; i++) {
    const digit = decimal.charAt(i)

    if (digit === '0') {
      continue
    }

    // 只有当分位有非零值、角位为零，且在非零元的上下文中，才在分前添加零
    if (i === 1 && decimal.charAt(0) === '0' && hasIntegral) {
      result += constants.DIGITS[0]
    }

    result += constants.DIGITS[Number(digit)] + constants.DECIMALS[i]
  }

  return result
}
