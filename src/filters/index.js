/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter(num) {
  return formatCurrency(num, true)
}

/**
 * 格式化货币
 * 12345 => $12,345.00
 *
 * @param {number} num
 * @param {boolean} hideDecimals 是否隐藏小数部分
 * @return 金额格式的字符串,如'1,234,567.45'
 * @type String
 */
export function formatCurrency(num, hideDecimals = false) {
  // 处理空值或零值
  if (!num) {
    return '0'
  }

  // 将输入转换为数字
  let value = Number(num)
  if (isNaN(value)) {
    // 尝试清理字符串中的非数字字符
    const cleanedString = String(num).replace(/[^\d.-]/g, '')
    value = Number(cleanedString)
    if (isNaN(value)) {
      return '0.00'
    }
  }

  // 格式化数字
  const absValue = Math.abs(value)
  const isNegative = value < 0
  const integerPart = Math.floor(absValue)
  const decimalPart = Math.round((absValue - integerPart) * 100)

  // 处理整数部分（添加千位分隔符）
  let formattedDollars = integerPart.toString()
  if (integerPart === 0) {
    formattedDollars = '0'
  } else {
    formattedDollars = integerPart.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }

  // 处理小数部分
  let formattedCents = decimalPart.toString().padStart(2, '0')

  // 添加负号（如果需要）
  if (isNegative) {
    formattedDollars = '-' + formattedDollars
  }

  return hideDecimals ? formattedDollars : formattedDollars + '.' + formattedCents
}

// Re-export the Chinese money converter
export { convertToChineseMoney } from './convertToChineseMoney'
