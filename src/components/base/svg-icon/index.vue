<template>
  <div class="svg-icon" :style="{ width: size + 'px', height: size + 'px' }">
    <svg class="icon" aria-hidden="true">
      <use :xlink:href="symbolId" />
    </svg>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true
  },
  size: {
    type: [Number, String],
    default: 16
  },
  color: {
    type: String,
    default: ''
  }
})

const symbolId = computed(() => `#icon-${props.name}`)
</script>

<style lang="scss" scoped>
.svg-icon {
  display: inline-block;
  overflow: hidden;
  fill: currentColor;
  
  .icon {
    width: 100%;
    height: 100%;
  }
}
</style>
