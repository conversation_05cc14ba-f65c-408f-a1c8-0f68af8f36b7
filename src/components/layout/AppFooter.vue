<template>
  <footer class="app-footer">
    <div class="footer-container">
      <div class="footer-sections">
        <div class="footer-section">
          <h3 class="section-title">Vue3 Element Plus</h3>
          <p class="section-description">
            现代化的开发者友好前端框架，基于 Vue 3、TypeScript 和 Element Plus，
            提供企业级组件和功能，助力快速开发高质量应用。
          </p>
        </div>
        <div class="footer-section">
          <h3 class="section-title">快速链接</h3>
          <ul class="footer-links">
            <li><router-link to="/" class="footer-link">首页</router-link></li>
            <li><router-link to="/dashboard" class="footer-link">数据看板</router-link></li>
            <li><router-link to="/components/data-grid" class="footer-link">组件</router-link></li>
            <li><router-link to="/about" class="footer-link">关于</router-link></li>
            <li><router-link to="/design-system" class="footer-link">设计系统</router-link></li>
          </ul>
        </div>
        <div class="footer-section">
          <h3 class="section-title">资源</h3>
          <ul class="footer-links">
            <li><a href="https://v3.vuejs.org/" target="_blank" class="footer-link">Vue 3 文档</a></li>
            <li><a href="https://element-plus.org/" target="_blank" class="footer-link">Element Plus</a></li>
            <li><a href="https://www.typescriptlang.org/" target="_blank" class="footer-link">TypeScript</a></li>
          </ul>
        </div>
      </div>
      <div class="footer-bottom">
        <p class="copyright">&copy; {{ currentYear }} Galaxy Vue Demi。</p>
        <div class="footer-bottom-links">
          <router-link to="/terms" class="bottom-link">使用条款</router-link>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { ref } from 'vue'

const currentYear = ref(new Date().getFullYear())
</script>

<style lang="scss" scoped>
.app-footer {
  background-color: var(--color-background-soft);
  border-top: 1px solid var(--color-border);
  padding: 4rem 2rem 2rem;
  color: var(--color-text);

  .footer-container {
    max-width: 1280px;
    margin: 0 auto;
  }

  .footer-sections {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .footer-section {
    .section-title {
      font-size: 1.125rem;
      font-weight: 600;
      margin-bottom: 1.25rem;
      color: var(--color-heading);
    }

    .section-description {
      font-size: 0.875rem;
      line-height: 1.6;
      margin-bottom: 1.25rem;
      color: var(--color-text-light);
    }

    .social-links {
      display: flex;
      gap: 1rem;

      .social-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        background-color: var(--color-background-mute);
        border-radius: 0.375rem;
        color: var(--color-text);
        transition: all 0.2s;

        &:hover {
          background-color: var(--color-primary);
          color: white;
          transform: translateY(-3px);
        }
      }
    }

    .footer-links {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 0.75rem;
      }

      .footer-link {
        color: var(--color-text-light);
        text-decoration: none;
        font-size: 0.875rem;
        transition: color 0.2s;

        &:hover {
          color: var(--color-primary);
        }
      }
    }

    .subscribe-form {
      display: flex;
      margin-top: 1rem;

      .subscribe-input {
        flex: 1;
        padding: 0.625rem 1rem;
        background-color: var(--color-background);
        border: 1px solid var(--color-border);
        border-radius: 0.375rem 0 0 0.375rem;
        font-size: 0.875rem;
        color: var(--color-text);

        &:focus {
          outline: none;
          border-color: var(--color-primary);
        }
      }

      .subscribe-button {
        padding: 0.625rem 1rem;
        background-color: var(--color-primary);
        color: white;
        border: none;
        border-radius: 0 0.375rem 0.375rem 0;
        font-size: 0.875rem;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background-color: var(--color-primary-dark);
        }
      }
    }
  }

  .footer-bottom {
    padding-top: 2rem;
    border-top: 1px solid var(--color-border);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .copyright {
      font-size: 0.875rem;
      color: var(--color-text-light);
    }

    .footer-bottom-links {
      display: flex;
      gap: 1.5rem;

      .bottom-link {
        font-size: 0.875rem;
        color: var(--color-text-light);
        text-decoration: none;

        &:hover {
          color: var(--color-primary);
        }
      }
    }
  }
}

@media (max-width: 992px) {
  .app-footer {
    .footer-sections {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

@media (max-width: 576px) {
  .app-footer {
    padding: 3rem 1rem 1.5rem;

    .footer-sections {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    .footer-bottom {
      flex-direction: column;
      gap: 1rem;
      text-align: center;

      .copyright {
        margin-bottom: 0.5rem;
      }
    }
  }
}
</style>
