# 开发环境变量
NODE_ENV=development
ENV='development'

# 应用标题
VUE_APP_TITLE=Galaxy Vue Demi (开发环境)

# API 基础路径
VUE_APP_BASE_API='/dev-api'

# 是否启用 Mock 数据
VUE_APP_MOCK=true

# 是否启用调试模式
VUE_APP_DEBUG=true

# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js
VUE_CLI_BABEL_TRANSPILE_MODULES=true

