import { convertToChineseMoney } from '@/filters/convertToChineseMoney'

describe('Chinese Money Filters', () => {
  describe('convertToChineseMoney', () => {
    it('handles empty or falsy values', () => {
      expect(convertToChineseMoney('')).toBe('')
      expect(convertToChineseMoney(null)).toBe('')
      expect(convertToChineseMoney(undefined)).toBe('')
      expect(convertToChineseMoney(0)).toBe('零元整')
    })

    it('converts integer numbers correctly', () => {
      expect(convertToChineseMoney(1)).toBe('壹元整')
      expect(convertToChineseMoney(10)).toBe('壹拾元整')
      expect(convertToChineseMoney(100)).toBe('壹佰元整')
      expect(convertToChineseMoney(1000)).toBe('壹仟元整')
      expect(convertToChineseMoney(10000)).toBe('壹万元整')
      expect(convertToChineseMoney(100000)).toBe('壹拾万元整')
      expect(convertToChineseMoney(1000000)).toBe('壹佰万元整')
      expect(convertToChineseMoney(10000000)).toBe('壹仟万元整')
      expect(convertToChineseMoney(100000000)).toBe('壹亿元整')
    })

    it('converts decimal numbers correctly', () => {
      expect(convertToChineseMoney(0.1)).toBe('壹角')
      expect(convertToChineseMoney(0.01)).toBe('壹分')
      expect(convertToChineseMoney(0.11)).toBe('壹角壹分')
      expect(convertToChineseMoney(1.1)).toBe('壹元壹角')
      expect(convertToChineseMoney(1.01)).toBe('壹元零壹分')
      expect(convertToChineseMoney(1.11)).toBe('壹元壹角壹分')
    })

    it('handles numbers with zeros correctly', () => {
      expect(convertToChineseMoney(101)).toBe('壹佰零壹元整')
      expect(convertToChineseMoney(1001)).toBe('壹仟零壹元整')
      expect(convertToChineseMoney(10001)).toBe('壹万零壹元整')
      expect(convertToChineseMoney(10010)).toBe('壹万零壹拾元整')
      expect(convertToChineseMoney(100100)).toBe('壹拾万零壹佰元整')
      expect(convertToChineseMoney(1001000)).toBe('壹佰万零壹仟元整')
    })

    it('handles complex numbers correctly', () => {
      expect(convertToChineseMoney(9876543.21)).toBe('玖佰捌拾柒万陆仟伍佰肆拾叁元贰角壹分')
      expect(convertToChineseMoney(1234567890)).toBe('壹拾贰亿叁仟肆佰伍拾陆万柒仟捌佰玖拾元整')
    })

    it('handles formatted numbers with commas', () => {
      expect(convertToChineseMoney('1,234')).toBe('壹仟贰佰叁拾肆元整')
      expect(convertToChineseMoney('1,234,567.89')).toBe('壹佰贰拾叁万肆仟伍佰陆拾柒元捌角玖分')
    })

    it('rejects invalid inputs', () => {
      expect(convertToChineseMoney('abc')).toBe('')
      expect(convertToChineseMoney('123abc')).toBe('')
      expect(convertToChineseMoney('$123')).toBe('')
    })

    it('handles numbers at the maximum limit', () => {
      const almostMax = '999999999999.99'
      expect(convertToChineseMoney(almostMax)).toBe('玖仟玖佰玖拾玖亿玖仟玖佰玖拾玖万玖仟玖佰玖拾玖元玖角玖分')
    })

    it('rejects numbers exceeding the maximum limit', () => {
      const overMax = '1000000000000'
      expect(convertToChineseMoney(overMax)).toBe('')
    })
  })
})
