import { formatCurrency } from '@/filters'

describe('Filters', () => {
  describe('formatCurrency', () => {
    it('formats positive numbers correctly', () => {
      expect(formatCurrency(1234.56)).toBe('1,234.56')
      expect(formatCurrency(1000000)).toBe('1,000,000.00')
      expect(formatCurrency(0.99)).toBe('0.99')
      expect(formatCurrency('1234.56')).toBe('1,234.56')
    })

    it('formats negative numbers correctly', () => {
      expect(formatCurrency(-1234.56)).toBe('-1,234.56')
      expect(formatCurrency(-0.99)).toBe('-0.99')
    })

    it('handles zero and empty values', () => {
      expect(formatCurrency(0)).toBe('0')
      expect(formatCurrency('')).toBe('0')
      expect(formatCurrency(null)).toBe('0')
      expect(formatCurrency(undefined)).toBe('0')
    })

    it('handles invalid inputs', () => {
      expect(formatCurrency('abc')).toBe('0.00')
      expect(formatCurrency('$1,234.56')).toBe('1,234.56')
    })

    it('hides decimals when hideDecimals is true', () => {
      expect(formatCurrency(1234.56, true)).toBe('1,234')
      expect(formatCurrency(1000000, true)).toBe('1,000,000')
      expect(formatCurrency(-1234.56, true)).toBe('-1,234')
    })

    it('rounds to nearest cent', () => {
      expect(formatCurrency(1234.567)).toBe('1,234.57')
      expect(formatCurrency(1234.564)).toBe('1,234.56')
      expect(formatCurrency(0.005)).toBe('0.01')
      expect(formatCurrency(0.004)).toBe('0.00')
    })
  })
})
